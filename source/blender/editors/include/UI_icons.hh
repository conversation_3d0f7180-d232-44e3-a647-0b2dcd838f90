/* SPDX-FileCopyrightText: 2009 Blender Authors
 *
 * SPDX-License-Identifier: GPL-2.0-or-later */

/** \file
 * \ingroup editorui
 */
/* BFA NOTE: When merging this, best to go in from the <PERSON>forartists one, then work the new icons from Blender. */
/* NOTE: this is included multiple times with different #defines for DEF_ICON. */

/* Auto define more specific types for places that do not need the distinction. */
#ifndef DEF_ICON_SCENE
#  define DEF_ICON_SCENE DEF_ICON
#endif
#ifndef DEF_ICON_COLLECTION
#  define DEF_ICON_COLLECTION DEF_ICON
#endif
#ifndef DEF_ICON_OBJECT
#  define DEF_ICON_OBJECT DEF_ICON
#endif
#ifndef DEF_ICON_OBJECT_DATA
#  define DEF_ICON_OBJECT_DATA DEF_ICON
#endif
#ifndef DEF_ICON_MODIFIER
#  define DEF_ICON_MODIFIER DEF_ICON
#endif
#ifndef DEF_ICON_SHADING
#  define DEF_ICON_SHADING DEF_ICON
#endif
#ifndef DEF_ICON_FOLDER
#  define DEF_ICON_FOLDER DEF_ICON
#endif
#ifndef DEF_ICON_COLOR
#  define DEF_ICON_COLOR DEF_ICON
#endif
/*#ifndef DEF_ICON_FUND*/ /*BFa - This icon is not used*/
/*#  define DEF_ICON_FUND DEF_ICON*/  /*BFa - This icon is not used*/
/*#endif*/ /*BFa - This icon is not used*/

/* ICON_ prefix added */

/* ------------------------- Icons ---------------------------*/

DEF_ICON_COLOR(NONE) /* bfa - never change! Needs to remain at the top of the list */

/* Keep these here, in this order. Their IDs should be static. */
DEF_ICON(CHAR_NOTDEF)
DEF_ICON(CHAR_REPLACEMENT)
DEF_ICON(NOT_FOUND)

/* letter def icon*/

DEF_ICON(ANIM)           /*BFA - DEF_ICON*/
DEF_ICON(AREA_DOCK)      /*BFA - needs updating from Blender*/
DEF_ICON(AREA_JOIN_DOWN) /*BFA - needs updating from Blender*/
DEF_ICON(AREA_JOIN_LEFT) /*BFA - needs updating from Blender*/
DEF_ICON(AREA_JOIN_UP)   /*BFA - needs updating from Blender*/
DEF_ICON(COLOR_TAG)      /*bfa a plain white icon for colors in vse. DEF_ICON is required!*/
DEF_ICON(DECORATE_LIBRARY_OVERRIDE)
DEF_ICON(DECORATE_LINKED)
DEF_ICON(FUND)
DEF_ICON(HEART)
DEF_ICON(IMAGE_PLANE)
DEF_ICON(LOCKVIEW_OFF)
DEF_ICON(LOCKVIEW_ON)
DEF_ICON(NODE_TEXTURE) /*BFA - DEF_ICON*/
DEF_ICON(PLAY_SOUND)
DEF_ICON(PRESET_NEW)
DEF_ICON(RESTRICT_INSTANCED_OFF)
DEF_ICON(RESTRICT_INSTANCED_ON)
DEF_ICON(RNA_ADD)
DEF_ICON(STRANDS)
DEF_ICON(TRASH)                   /*BFA - DEF_ICON*/
DEF_ICON_COLLECTION(GROUP_BRIGHT) /*BFA - collection colors*/

/* letter numbers */

DEF_ICON_COLOR(4L_OFF)
DEF_ICON_COLOR(4L_ON)

/* letter A */

DEF_ICON_COLOR(ABSOLUTEPATH)
DEF_ICON_COLOR(ACCUMULATE)
DEF_ICON_COLOR(ACTION) /* Dope Sheet */
DEF_ICON_COLOR(ACTION_SLOT)
DEF_ICON_COLOR(ACTION_TWEAK)
DEF_ICON_COLOR(ACTION_TWEAK_SOLO)
DEF_ICON_COLOR(ACTIVE_ELEMENT)
DEF_ICON_COLOR(ADD)
DEF_ICON_COLOR(ADD_ALL)
DEF_ICON_COLOR(ADD_IK)
DEF_ICON_COLOR(ADD_METASTRIP)
DEF_ICON_COLOR(ADD_SELECTED)
DEF_ICON_COLOR(ADD_STRIP)
DEF_ICON_COLOR(ADD_TO_ACTIVE)
DEF_ICON_COLOR(ADD_TRACK)
DEF_ICON_COLOR(ADD_TRACK_ABOVE)
DEF_ICON_COLOR(AFTER_CURRENT_FRAME)
DEF_ICON_COLOR(ALIASED)
DEF_ICON_COLOR(ALIGN)
DEF_ICON_COLOR(ALIGNAUTO)
DEF_ICON_COLOR(ALIGNCAMERA_ACTIVE)
DEF_ICON_COLOR(ALIGNCAMERA_VIEW)
DEF_ICON_COLOR(ALIGNHORIZONTAL)
DEF_ICON_COLOR(ALIGNVERTICAL)
DEF_ICON_COLOR(ALIGN_BOTTOM)
DEF_ICON_COLOR(ALIGN_CENTER)
DEF_ICON_COLOR(ALIGN_EULER_TO_VECTOR)
DEF_ICON_COLOR(ALIGN_FLUSH)
DEF_ICON_COLOR(ALIGN_JUSTIFY)
DEF_ICON_COLOR(ALIGN_LEFT)
DEF_ICON_COLOR(ALIGN_MIDDLE)
DEF_ICON_COLOR(ALIGN_RIGHT)
DEF_ICON_COLOR(ALIGN_ROTATION_TO_VECTOR)
DEF_ICON_COLOR(ALIGN_TOP)
DEF_ICON_COLOR(ALIGN_TRANSFORM)
DEF_ICON_COLOR(ALTERNATED)
DEF_ICON_COLOR(ANCHOR_BOTTOM)
DEF_ICON_COLOR(ANCHOR_CENTER)
DEF_ICON_COLOR(ANCHOR_LEFT)
DEF_ICON_COLOR(ANCHOR_RIGHT)
DEF_ICON_COLOR(ANCHOR_TOP)
DEF_ICON_COLOR(ANGLE)
DEF_ICON_COLOR(ANIM_DATA)
DEF_ICON_COLOR(ANTIALIASED)
DEF_ICON_COLOR(APPEND_BLEND)
DEF_ICON_COLOR(APPLYALL)
DEF_ICON_COLOR(APPLYALLDELTA)
DEF_ICON_COLOR(APPLYANIDELTA)
DEF_ICON_COLOR(APPLYMOVE)
DEF_ICON_COLOR(APPLYMOVEDELTA)
DEF_ICON_COLOR(APPLYROTATE)
DEF_ICON_COLOR(APPLYROTATEDELTA)
DEF_ICON_COLOR(APPLYSCALE)
DEF_ICON_COLOR(APPLYSCALEDELTA)
DEF_ICON_COLOR(APPLY_PARENT_INVERSE)
DEF_ICON_COLOR(APPLY_ROTSCALE)
DEF_ICON_COLOR(APPTEMPLATE)
DEF_ICON_COLOR(AREA)
DEF_ICON_COLOR(AREA_SWAP)
DEF_ICON_COLOR(ARMATURE_DATA)
DEF_ICON_COLOR(ARROW_LEFTRIGHT)
DEF_ICON_COLOR(ASSET_MANAGER)
DEF_ICON_COLOR(ASSIGN)
DEF_ICON_COLOR(ATTRIBUTE_CAPTURE)
DEF_ICON_COLOR(ATTRIBUTE_CLAMP)
DEF_ICON_COLOR(ATTRIBUTE_COLORRAMP)
DEF_ICON_COLOR(ATTRIBUTE_COMBINE_XYZ)
DEF_ICON_COLOR(ATTRIBUTE_COMPARE)
DEF_ICON_COLOR(ATTRIBUTE_CONVERT)
DEF_ICON_COLOR(ATTRIBUTE_CURVEMAP)
DEF_ICON_COLOR(ATTRIBUTE_FILL)
DEF_ICON_COLOR(ATTRIBUTE_MAPRANGE)
DEF_ICON_COLOR(ATTRIBUTE_MATH)
DEF_ICON_COLOR(ATTRIBUTE_MIX)
DEF_ICON_COLOR(ATTRIBUTE_PROXIMITY)
DEF_ICON_COLOR(ATTRIBUTE_RANDOMIZE)
DEF_ICON_COLOR(ATTRIBUTE_REMOVE)
DEF_ICON_COLOR(ATTRIBUTE_SEPARATE_XYZ)
DEF_ICON_COLOR(ATTRIBUTE_STATISTIC)
DEF_ICON_COLOR(ATTRIBUTE_STORE)
DEF_ICON_COLOR(ATTRIBUTE_TEXTURE)
DEF_ICON_COLOR(ATTRIBUTE_TRANSFER)
DEF_ICON_COLOR(ATTRIBUTE_VECTORMATH)
DEF_ICON_COLOR(ATTRIBUTE_VECTOR_ROTATE)
DEF_ICON_COLOR(AUTO)
DEF_ICON_COLOR(AUTOCOMPLETE)

DEF_ICON_COLOR(AUTOMERGE_OFF) /*bfa- don't change the order of the automerge icons*/
DEF_ICON_COLOR(AUTOMERGE_ON)  /*bfa- don't change the order of the automerge icons*/

DEF_ICON_COLOR(AVERAGE)
DEF_ICON_COLOR(AVERAGEISLANDSCALE)
DEF_ICON_COLOR(AVOID)
DEF_ICON_COLOR(AXES_TO_ROTATION)
DEF_ICON_COLOR(AXIS_ANGLE_TO_ROTATION)
DEF_ICON_COLOR(AXIS_FRONT)
DEF_ICON_COLOR(AXIS_SIDE)
DEF_ICON_COLOR(AXIS_TOP)

/* letter B */

DEF_ICON_COLOR(BACK)
DEF_ICON_COLOR(BACKGROUND)
DEF_ICON_COLOR(BAKE)
DEF_ICON_COLOR(BAKE_ACTION)
DEF_ICON_COLOR(BAKE_CURVE)
DEF_ICON_COLOR(BAKE_SOUND)
DEF_ICON_COLOR(BATCH_GENERATE)
DEF_ICON_COLOR(BATCH_GENERATE_CLEAR)
DEF_ICON_COLOR(BEAUTIFY)
DEF_ICON_COLOR(BEFORE_CURRENT_FRAME)
DEF_ICON_COLOR(BEND)
DEF_ICON_COLOR(BETWEEN_MARKERS)
DEF_ICON_COLOR(BEVEL)
DEF_ICON_COLOR(BISECT)
DEF_ICON_COLOR(BITMATH)
DEF_ICON_COLOR(BLANK1) /* Not actually blank - this is used all over the place */
DEF_ICON_COLOR(BLENDER)
DEF_ICON_COLOR(BLENDFROMSHAPE)
DEF_ICON_COLOR(BLEND_OFFSET)
DEF_ICON_COLOR(BLEND_TEX)
DEF_ICON_COLOR(BLEND_TO_DEFAULT)
DEF_ICON_COLOR(BLEND_TO_EASE)
DEF_ICON_COLOR(BLEND_TO_NEIGHBOUR)
DEF_ICON_COLOR(BLUR_ATTRIBUTE)
DEF_ICON_COLOR(BOIDS)
DEF_ICON_COLOR(BOLD)
DEF_ICON_COLOR(BONE_DATA)
DEF_ICON_COLOR(BONE_LAYER)
DEF_ICON_COLOR(BOOKMARKS)
DEF_ICON_COLOR(BOOLEAN_INTERSECT)
DEF_ICON_COLOR(BOOLEAN_MATH)
DEF_ICON_COLOR(BORDERMOVE)
DEF_ICON_COLOR(BORDER_LASSO)
DEF_ICON_COLOR(BORDER_RECT)
DEF_ICON_COLOR(BOX_ADD)
DEF_ICON_COLOR(BOX_HIDE)
DEF_ICON_COLOR(BOX_MASK)
DEF_ICON_COLOR(BOX_SHOW)
DEF_ICON_COLOR(BOX_TRIM)
DEF_ICON_COLOR(BREAKDOWNER_POSE)
DEF_ICON_COLOR(BRIDGE_EDGELOOPS)
DEF_ICON_COLOR(BRIGHTNESS_CONTRAST)
DEF_ICON_COLOR(BRUSHANGLE)
DEF_ICON_COLOR(BRUSHES_ALL)
DEF_ICON_COLOR(BRUSHSIZE)
DEF_ICON_COLOR(BRUSHSTRENGTH)
DEF_ICON_COLOR(BRUSH_DATA)
DEF_ICON_COLOR(BRUSH_RESET)
DEF_ICON_COLOR(BUILTIN_MODIFIER)
DEF_ICON_COLOR(BUTS)        /* Properties */
DEF_ICON_COLOR(BUTS_ACTIVE) /* Properties */

/* letter C */

DEF_ICON_COLOR(CAMERA_DATA)
DEF_ICON_COLOR(CAMERA_STEREO)
DEF_ICON_COLOR(CANCEL)
DEF_ICON_COLOR(CARET_LINE_BEGIN)
DEF_ICON_COLOR(CARET_LINE_END)
DEF_ICON_COLOR(CARET_NEXT_CHAR)
DEF_ICON_COLOR(CARET_NEXT_WORD)
DEF_ICON_COLOR(CARET_PREV_CHAR)
DEF_ICON_COLOR(CARET_PREV_WORD)
DEF_ICON_COLOR(CENTER)
DEF_ICON_COLOR(CENTERTOCURSOR)
DEF_ICON_COLOR(CENTERTOMOUSE)
DEF_ICON_COLOR(CENTER_ONLY)
DEF_ICON_COLOR(CHECKBOX_DEHLT)
DEF_ICON_COLOR(CHECKBOX_HLT)
DEF_ICON_COLOR(CHECKER_DESELECT)
DEF_ICON_COLOR(CHECKMARK)
DEF_ICON_COLOR(CHILD)
DEF_ICON_COLOR(CHILD_RECURSIVE)
DEF_ICON_COLOR(CIRCLE)
DEF_ICON_COLOR(CIRCLE_SELECT)
DEF_ICON_COLOR(CLEAN_CHANNELS)
DEF_ICON_COLOR(CLEAN_CHANNELS_FRAMES)
DEF_ICON_COLOR(CLEAN_KEYS)
DEF_ICON_COLOR(CLEAR)
DEF_ICON_COLOR(CLEARFSFACE)
DEF_ICON_COLOR(CLEARMOVE)
DEF_ICON_COLOR(CLEARORIGIN)
DEF_ICON_COLOR(CLEARROTATE)
DEF_ICON_COLOR(CLEARSCALE)
DEF_ICON_COLOR(CLEARSHARPEDGES)
DEF_ICON_COLOR(CLEARSHARPVERTS)
DEF_ICON_COLOR(CLEAR_CONSTRAINT)
DEF_ICON_COLOR(CLEAR_FS_EDGE)
DEF_ICON_COLOR(CLEAR_IK)
DEF_ICON_COLOR(CLEAR_MASK)
DEF_ICON_COLOR(CLEAR_ROLL)
DEF_ICON_COLOR(CLEAR_SEAM)
DEF_ICON_COLOR(CLEAR_TILT)
DEF_ICON_COLOR(CLEAR_TRACK)
DEF_ICON_COLOR(CLIP)
DEF_ICON_COLOR(CLIPPINGBORDER)
DEF_ICON_COLOR(CLIPUV_DEHLT)
DEF_ICON_COLOR(CLIPUV_HLT)
DEF_ICON_COLOR(CLOUD_TEX)
DEF_ICON_COLOR(COLLAPSEMENU)
DEF_ICON_COLOR(COLLECTION_BONE_ADD)
DEF_ICON_COLOR(COLLECTION_BONE_NEW)
DEF_ICON_COLOR(COLLECTION_BONE_REMOVE)
DEF_ICON_COLOR(COLLECTION_INFO)
DEF_ICON_COLOR(COLLECTION_NEW)
DEF_ICON_COLOR(COLOR) /* see COLOR_RED/GREEN/BLUE */
DEF_ICON_COLOR(COLOR_BLUE)
DEF_ICON_COLOR(COLOR_GREEN)
DEF_ICON_COLOR(COLOR_RED)
DEF_ICON_COLOR(COLOR_SPACE)
DEF_ICON_COLOR(COLUMNS_KEYS)
DEF_ICON_COLOR(COLUMNS_MARKERS)
DEF_ICON_COLOR(COLUMN_CURRENT_FRAME)
DEF_ICON_COLOR(COMBINE_COLOR)
DEF_ICON_COLOR(COMBINE_MATRIX)
DEF_ICON_COLOR(COMBINE_TRANSFORM)
DEF_ICON_COLOR(COMMENT)
DEF_ICON_COLOR(COMMUNITY)
DEF_ICON_COLOR(CONE)
DEF_ICON_COLOR(CONNECTED)
DEF_ICON_COLOR(CONSOLE)
DEF_ICON_COLOR(CONSTRAINT)
DEF_ICON_COLOR(CONSTRAINT_BONE)
DEF_ICON_COLOR(CONSTRAINT_DATA)
DEF_ICON_COLOR(CONTROLPOINTROW)
DEF_ICON_COLOR(CONVEXHULL)
DEF_ICON_COLOR(CON_ACTION)
DEF_ICON_COLOR(CON_ARMATURE)
DEF_ICON_COLOR(CON_CAMERASOLVER)
DEF_ICON_COLOR(CON_CHILDOF)
DEF_ICON_COLOR(CON_CLAMPTO)
DEF_ICON_COLOR(CON_DISTLIMIT)
DEF_ICON_COLOR(CON_FLOOR)
DEF_ICON_COLOR(CON_FOLLOWPATH)
DEF_ICON_COLOR(CON_FOLLOWTRACK)
DEF_ICON_COLOR(CON_KINEMATIC)
DEF_ICON_COLOR(CON_LOCKTRACK)
DEF_ICON_COLOR(CON_LOCLIKE)
DEF_ICON_COLOR(CON_LOCLIMIT)
DEF_ICON_COLOR(CON_OBJECTSOLVER)
DEF_ICON_COLOR(CON_PIVOT)
DEF_ICON_COLOR(CON_ROTLIKE)
DEF_ICON_COLOR(CON_ROTLIMIT)
DEF_ICON_COLOR(CON_SAMEVOL)
DEF_ICON_COLOR(CON_SHRINKWRAP)
DEF_ICON_COLOR(CON_SIZELIKE)
DEF_ICON_COLOR(CON_SIZELIMIT)
DEF_ICON_COLOR(CON_SPLINEIK)
DEF_ICON_COLOR(CON_STRETCHTO)
DEF_ICON_COLOR(CON_TRACKTO)
DEF_ICON_COLOR(CON_TRANSFORM)
DEF_ICON_COLOR(CON_TRANSFORM_CACHE)
DEF_ICON_COLOR(CON_TRANSLIKE)
DEF_ICON_COLOR(COPYDOWN)
DEF_ICON_COLOR(COPYMIRRORED)
DEF_ICON_COLOR(COPYRIGHT)
DEF_ICON_COLOR(COPY_ID)
DEF_ICON_COLOR(CORNERS_OF_EDGE)
DEF_ICON_COLOR(CORNERS_OF_FACE)
DEF_ICON_COLOR(CORNERS_OF_VERTEX)
DEF_ICON_COLOR(CREASE)
DEF_ICON_COLOR(CRYPTOMATTE)
DEF_ICON_COLOR(CUBE)
DEF_ICON_COLOR(CUBEPROJECT)
DEF_ICON_COLOR(CURRENT_FILE)
DEF_ICON_COLOR(CURSOR)
DEF_ICON_COLOR(CURSORTOACTIVE)
DEF_ICON_COLOR(CURSORTOCENTER)
DEF_ICON_COLOR(CURSORTOGRID)
DEF_ICON_COLOR(CURSORTOSELECTION)
DEF_ICON_COLOR(CURSOR_TO_PIXELS)
DEF_ICON_COLOR(CURVES)
DEF_ICON_COLOR(CURVE_ARC)
DEF_ICON_COLOR(CURVE_BEZCIRCLE)
DEF_ICON_COLOR(CURVE_BEZCURVE)
DEF_ICON_COLOR(CURVE_DATA)
DEF_ICON_COLOR(CURVE_FILL)
DEF_ICON_COLOR(CURVE_FILLET)
DEF_ICON_COLOR(CURVE_HANDLE_POSITIONS)
DEF_ICON_COLOR(CURVE_LINE)
DEF_ICON_COLOR(CURVE_NCIRCLE)
DEF_ICON_COLOR(CURVE_NCURVE)
DEF_ICON_COLOR(CURVE_NORMAL)
DEF_ICON_COLOR(CURVE_OF_POINT)
DEF_ICON_COLOR(CURVE_PARAMETER)
DEF_ICON_COLOR(CURVE_PATH)
DEF_ICON_COLOR(CURVE_QUADRILATERAL)
DEF_ICON_COLOR(CURVE_RESAMPLE)
DEF_ICON_COLOR(CURVE_SAMPLE)
DEF_ICON_COLOR(CURVE_SPIRAL)
DEF_ICON_COLOR(CURVE_STAR)
DEF_ICON_COLOR(CURVE_STARTEND)
DEF_ICON_COLOR(CURVE_TANGENT)
DEF_ICON_COLOR(CURVE_TILT)
DEF_ICON_COLOR(CURVE_TRIM)
DEF_ICON_COLOR(CUT)
DEF_ICON_COLOR(CUT_LINKS)
DEF_ICON_COLOR(CYCLES_MODIFIER)
DEF_ICON_COLOR(CYLINDERPROJECT)

/* letter D */

DEF_ICON_COLOR(DATABLOCK_CLEAR)
DEF_ICON_COLOR(DEBUG)
DEF_ICON_COLOR(DECIMATE)
DEF_ICON_COLOR(DECORATE)
DEF_ICON_COLOR(DECORATE_ANIMATE)
DEF_ICON_COLOR(DECORATE_DRIVER)
DEF_ICON_COLOR(DECORATE_KEYFRAME)
DEF_ICON_COLOR(DECORATE_OVERRIDE)

DEF_ICON_COLOR(DECORATE_UNLOCKED) /*BFA - don't change the order, exception to the order for the icon +1 toggle*/
DEF_ICON_COLOR(DECORATE_LOCKED) /*BFA - don't change the order, exception to the order for the icon +1 toggle*/

DEF_ICON_COLOR(DECREASE_KERNING)
DEF_ICON_COLOR(DEC_CONTRAST)
DEF_ICON_COLOR(DEFORM_CURVES)
DEF_ICON_COLOR(DEGENERATE_DISSOLVE)
DEF_ICON_COLOR(DEGREE)
DEF_ICON_COLOR(DELETE)
DEF_ICON_COLOR(DELETE_ALL)
DEF_ICON_COLOR(DELETE_DUPLICATE)
DEF_ICON_COLOR(DELETE_LOOSE)
DEF_ICON_COLOR(DEPTH)
DEF_ICON_COLOR(DESKTOP)
DEF_ICON_COLOR(DETACH_LINKS)
DEF_ICON_COLOR(DETACH_LINKS_MOVE)
DEF_ICON_COLOR(DETECT)
DEF_ICON_COLOR(DIAL_GIZMO)
DEF_ICON_COLOR(DIRTY_VERTEX)
DEF_ICON_COLOR(DISABLE)
DEF_ICON_COLOR(DISC)

DEF_ICON_COLOR(DISCLOSURE_TRI_RIGHT) /*BFA - don't change the order, exception to the order for the icon +1 toggle*/
DEF_ICON_COLOR(DISCLOSURE_TRI_DOWN) /*BFA - don't change the order, exception to the order for the icon +1 toggle*/

DEF_ICON_COLOR(DISCONTINUE_EULER)
DEF_ICON_COLOR(DISK_DRIVE)
DEF_ICON_COLOR(DISSOLVE_BETWEEN)
DEF_ICON_COLOR(DISSOLVE_EDGES)
DEF_ICON_COLOR(DISSOLVE_FACES)
DEF_ICON_COLOR(DISSOLVE_LIMITED)
DEF_ICON_COLOR(DISSOLVE_SELECTION)
DEF_ICON_COLOR(DISSOLVE_UNSELECTED)
DEF_ICON_COLOR(DISSOLVE_VERTS)
DEF_ICON_COLOR(DISTORTED_NOISE_TEX)
DEF_ICON_COLOR(DOCUMENTS)
DEF_ICON_COLOR(DOF)
DEF_ICON_COLOR(DOLLY)
DEF_ICON_COLOR(DOMAIN_SIZE)
DEF_ICON_COLOR(DOPESHEET_ACTIVE)
DEF_ICON_COLOR(DOT)
DEF_ICON_COLOR(DOUBLE_LEFT)
DEF_ICON_COLOR(DOUBLE_RIGHT)
DEF_ICON_COLOR(DOWNARROW_HLT)
DEF_ICON_COLOR(DRAWSIZE)
DEF_ICON_COLOR(DRIVER)
DEF_ICON_COLOR(DRIVER_ACTIVE)
DEF_ICON_COLOR(DRIVER_DISTANCE)
DEF_ICON_COLOR(DRIVER_ROTATIONAL_DIFFERENCE)
DEF_ICON_COLOR(DRIVER_TRANSFORM)
DEF_ICON_COLOR(DUAL_MESH)
DEF_ICON_COLOR(DUPLICATE)
DEF_ICON_COLOR(DUPLICATE_ALL)
DEF_ICON_COLOR(DUPLI_EXTRUDE)
DEF_ICON_COLOR(DUPLI_EXTRUDE_ROTATE)
DEF_ICON_COLOR(DUTCH_FLORIN)

/* letter E */

DEF_ICON_COLOR(EDGESEL)
DEF_ICON_COLOR(EDGES_OF_CORNER)
DEF_ICON_COLOR(EDGES_OF_VERTEX)
DEF_ICON_COLOR(EDGE_ANGLE)
DEF_ICON_COLOR(EDGE_COLLAPSE)
DEF_ICON_COLOR(EDGE_NEIGHBORS)
DEF_ICON_COLOR(EDGE_PATHS_TO_CURVES)
DEF_ICON_COLOR(EDGE_PATH_TO_SELECTION)
DEF_ICON_COLOR(EDGE_VERTICES)
DEF_ICON_COLOR(EDIT)
DEF_ICON_COLOR(EDITMODE_HLT)
DEF_ICON_COLOR(EDIT_EXTERNAL)
DEF_ICON_COLOR(EMPTY_ARROWS)
DEF_ICON_COLOR(EMPTY_AXIS)
DEF_ICON_COLOR(EMPTY_CIRCLE)
DEF_ICON_COLOR(EMPTY_CONE)
DEF_ICON_COLOR(EMPTY_CUBE)
DEF_ICON_COLOR(EMPTY_DATA)
DEF_ICON_COLOR(EMPTY_IMAGE)
DEF_ICON_COLOR(EMPTY_SINGLE_ARROW)
DEF_ICON_COLOR(EMPTY_SPHERE)
DEF_ICON_COLOR(ENABLE)
DEF_ICON_COLOR(ENHANCE)
DEF_ICON_COLOR(EQUALIZE_HANDLER)
DEF_ICON_COLOR(ERASE)
DEF_ICON_COLOR(ERROR)
DEF_ICON_COLOR(EULER_TO_ROTATION)
DEF_ICON_COLOR(EURO)
DEF_ICON_COLOR(EXPANDMENU)
DEF_ICON_COLOR(EXPERIMENTAL)
DEF_ICON_COLOR(EXPORT)
DEF_ICON_COLOR(EXPORT_COLLECTION)
DEF_ICON_COLOR(EXPOSURE)
DEF_ICON_COLOR(EXTEND_VERTICES)
DEF_ICON_COLOR(EXTERNAL_DATA)
DEF_ICON_COLOR(EXTERNAL_DRIVE)
DEF_ICON_COLOR(EXTENSIONS_ALL)
DEF_ICON_COLOR(EXTRAPOLATION_CONSTANT)
DEF_ICON_COLOR(EXTRAPOLATION_CYCLIC)
DEF_ICON_COLOR(EXTRAPOLATION_CYCLIC_CLEAR)
DEF_ICON_COLOR(EXTRAPOLATION_LINEAR)
DEF_ICON_COLOR(EXTRUDESIZE)
DEF_ICON_COLOR(EXTRUDE_REGION)
DEF_ICON_COLOR(EYEDROPPER)

/* letter F */

DEF_ICON_COLOR(FACEGROUP)
DEF_ICON_COLOR(FACEREGIONS)
DEF_ICON_COLOR(FACESEL)
DEF_ICON_COLOR(FACESEL_HLT)
DEF_ICON_COLOR(FACE_CORNER)
DEF_ICON_COLOR(FACE_MAPS)
DEF_ICON_COLOR(FACE_MAPS_ACTIVE)
DEF_ICON_COLOR(FACE_NEIGHBORS)
DEF_ICON_COLOR(FACE_OF_CORNER)
DEF_ICON_COLOR(FACE_SET)
DEF_ICON_COLOR(FAKE_USER_OFF)
DEF_ICON_COLOR(FAKE_USER_ON)
DEF_ICON_COLOR(FALLOFFSTROKE)
DEF_ICON_COLOR(FCURVE)
DEF_ICON_COLOR(FCURVE_SNAPSHOT)
DEF_ICON_COLOR(FF)
DEF_ICON_COLOR(FIELD_AT_INDEX)
DEF_ICON_COLOR(FIELD_DOMAIN)
DEF_ICON_COLOR(FIGHT)
DEF_ICON_COLOR(FILE)
DEF_ICON_COLOR(FILEBROWSER)
DEF_ICON_COLOR(FILE_3D)
DEF_ICON_COLOR(FILE_ALIAS)
DEF_ICON_COLOR(FILE_ARCHIVE)
DEF_ICON_COLOR(FILE_BACKUP)
DEF_ICON_COLOR(FILE_BLANK)
DEF_ICON_COLOR(FILE_BLEND)
DEF_ICON_COLOR(FILE_CACHE)
DEF_ICON_COLOR(FILE_FOLDER)
DEF_ICON_COLOR(FILE_FONT)
DEF_ICON_COLOR(FILE_HIDDEN)
DEF_ICON_COLOR(FILE_IMAGE)
DEF_ICON_COLOR(FILE_MOVIE)
DEF_ICON_COLOR(FILE_NEW)
DEF_ICON_COLOR(FILE_PARENT)
DEF_ICON_COLOR(FILE_REFRESH)
DEF_ICON_COLOR(FILE_SCRIPT)
DEF_ICON_COLOR(FILE_SOUND)
DEF_ICON_COLOR(FILE_TEXT)
DEF_ICON_COLOR(FILE_TICK)
DEF_ICON_COLOR(FILE_VOLUME)
DEF_ICON_COLOR(FILL)
DEF_ICON_COLOR(FILLBETWEEN)
DEF_ICON_COLOR(FILL_HOLE)
DEF_ICON_COLOR(FILL_MASK)
DEF_ICON_COLOR(FILTER)
DEF_ICON_COLOR(FIXED_SIZE)
DEF_ICON_COLOR(FLATTEN_HANDLER)
DEF_ICON_COLOR(FLIP)
DEF_ICON_COLOR(FLIP_NORMALS)
DEF_ICON_COLOR(FLIP_X)
DEF_ICON_COLOR(FLIP_Y)
DEF_ICON_COLOR(FLIP_Z)
DEF_ICON_COLOR(FLOAT_COMPARE)
DEF_ICON_COLOR(FLOAT_CURVE)
DEF_ICON_COLOR(FLOAT_TO_INT)
DEF_ICON_COLOR(FLOCK)
DEF_ICON_COLOR(FLOODFILL)
DEF_ICON_COLOR(FLOOR)
DEF_ICON_COLOR(FLY_NAVIGATION)
DEF_ICON_COLOR(FOLDER_REDIRECT)
DEF_ICON_COLOR(FOLLOWQUADS)
DEF_ICON_COLOR(FOLLOW_LEADER)
DEF_ICON_COLOR(FONTPREVIEW)
DEF_ICON_COLOR(FONT_DATA)
DEF_ICON_COLOR(FORCE_BOID)
DEF_ICON_COLOR(FORCE_CHARGE)
DEF_ICON_COLOR(FORCE_CURVE)
DEF_ICON_COLOR(FORCE_DRAG)
DEF_ICON_COLOR(FORCE_FLUIDFLOW)
DEF_ICON_COLOR(FORCE_FORCE)
DEF_ICON_COLOR(FORCE_HARMONIC)
DEF_ICON_COLOR(FORCE_LENNARDJONES)
DEF_ICON_COLOR(FORCE_MAGNETIC)
DEF_ICON_COLOR(FORCE_TEXTURE)
DEF_ICON_COLOR(FORCE_TURBULENCE)
DEF_ICON_COLOR(FORCE_VORTEX)
DEF_ICON_COLOR(FORCE_WIND)
DEF_ICON_COLOR(FORMAT_STRING)
DEF_ICON_COLOR(FOR_EACH)
DEF_ICON_COLOR(FORWARD)
DEF_ICON_COLOR(FRAME_NEXT)
DEF_ICON_COLOR(FRAME_PREV)
DEF_ICON_COLOR(FRAME_PREVIEW_RANGE)
DEF_ICON_COLOR(FRAME_SCENE_RANGE)
DEF_ICON_COLOR(FREEZE)
DEF_ICON_COLOR(FULLSCREEN)
DEF_ICON_COLOR(FULLSCREEN_ENTER)
DEF_ICON_COLOR(FULLSCREEN_EXIT)

/* letter G */

DEF_ICON_COLOR(GABOR_NOISE)
DEF_ICON_COLOR(GAME)
DEF_ICON_COLOR(GENERATOR_MODIFIER)
DEF_ICON_COLOR(GEOMETRY_INSTANCE)
DEF_ICON_COLOR(GEOMETRY_NAME)
DEF_ICON_COLOR(GEOMETRY_NODES)
DEF_ICON_COLOR(GEOMETRY_NODES_ACTIVE)
DEF_ICON_COLOR(GEOMETRY_PROXIMITY)
DEF_ICON_COLOR(GEOMETRY_SET)
DEF_ICON_COLOR(GEOMETRY_TO_ORIGIN)
DEF_ICON_COLOR(GERMAN_S)
DEF_ICON_COLOR(GESTURE_PAN)
DEF_ICON_COLOR(GESTURE_ROTATE)
DEF_ICON_COLOR(GESTURE_ZOOM)
DEF_ICON_COLOR(GET_ID)
DEF_ICON_COLOR(GHOST)
DEF_ICON_COLOR(GHOST_DISABLED)
DEF_ICON_COLOR(GHOST_ENABLED)
DEF_ICON_COLOR(GIZMO)
DEF_ICON_COLOR(GOAL)
DEF_ICON_COLOR(GOTO)
DEF_ICON_COLOR(GP_CAPS_FLAT)
DEF_ICON_COLOR(GP_CAPS_ROUND)
DEF_ICON_COLOR(GP_MULTIFRAME_EDITING)
DEF_ICON_COLOR(GP_ONLY_SELECTED)
DEF_ICON_COLOR(GP_SELECT_BETWEEN_STROKES)
DEF_ICON_COLOR(GP_SELECT_POINTS)
DEF_ICON_COLOR(GP_SELECT_STROKES)
DEF_ICON_COLOR(GRADIENT)
DEF_ICON_COLOR(GRAPH)
DEF_ICON_COLOR(GRAPH_ACTIVE)
DEF_ICON_COLOR(GREASEPENCIL)
DEF_ICON_COLOR(GREASEPENCIL_LAYER_GROUP)
DEF_ICON_COLOR(GRID)
DEF_ICON_COLOR(GRIDFILL)
DEF_ICON_COLOR(GRIP)
DEF_ICON_COLOR(GRIP_V)
DEF_ICON_COLOR(GROUNDGRID)
DEF_ICON_COLOR(GROUP)
DEF_ICON_COLOR(GROUPINPUT)
DEF_ICON_COLOR(GROUPOUTPUT)
DEF_ICON_COLOR(GROUP_BONE)
DEF_ICON_COLOR(GROUP_UVS)
DEF_ICON_COLOR(GROUP_VCOL)
DEF_ICON_COLOR(GROUP_VERTEX)


/* letter H */

DEF_ICON_COLOR(HAIR_DATA)
DEF_ICON_COLOR(HAND)
DEF_ICON_COLOR(HANDLE_ALIGNED)
DEF_ICON_COLOR(HANDLE_ALIGN_SINGLE)
DEF_ICON_COLOR(HANDLE_AUTO)
DEF_ICON_COLOR(HANDLE_AUTOCLAMPED)
DEF_ICON_COLOR(HANDLE_FREE)
DEF_ICON_COLOR(HANDLE_VECTOR)
DEF_ICON_COLOR(HASH)
DEF_ICON_COLOR(HELP)

DEF_ICON_COLOR(HIDE_ON)/* bfa - don't change the order*/
DEF_ICON_COLOR(HIDE_OFF)/* bfa - don't change the order*/

DEF_ICON_COLOR(HIDE_RENDERVIEW)
DEF_ICON_COLOR(HIDE_UNSELECTED)
DEF_ICON_COLOR(HIERARCHY)
DEF_ICON_COLOR(HIERARCHY_DOWN)
DEF_ICON_COLOR(HIERARCHY_UP)
DEF_ICON_COLOR(HISTORY_CYCLE_BACK)
DEF_ICON_COLOR(HISTORY_CYCLE_FORWARD)
DEF_ICON_COLOR(HOLDOUT_OFF)
DEF_ICON_COLOR(HOLDOUT_ON)
DEF_ICON_COLOR(HOLD_SPLIT)
DEF_ICON_COLOR(HOME)
DEF_ICON_COLOR(HOOK)
DEF_ICON_COLOR(HOOK_ASSIGN)
DEF_ICON_COLOR(HOOK_BONE)
DEF_ICON_COLOR(HOOK_NEW)
DEF_ICON_COLOR(HOOK_RECENTER)
DEF_ICON_COLOR(HOOK_REMOVE)
DEF_ICON_COLOR(HOOK_RESET)
DEF_ICON_COLOR(HOOK_SELECT)
DEF_ICON_COLOR(HOOK_SELECTED)
DEF_ICON_COLOR(HUE)
DEF_ICON_COLOR(HUECORRECT)
DEF_ICON_COLOR(HUESATVAL)

/* letter i */

DEF_ICON_COLOR(IMAGE)
DEF_ICON_COLOR(IMAGE_ALPHA)
DEF_ICON_COLOR(IMAGE_ASPECT)
DEF_ICON_COLOR(IMAGE_BACKGROUND)
DEF_ICON_COLOR(IMAGE_DATA)
DEF_ICON_COLOR(IMAGE_INFO)
DEF_ICON_COLOR(IMAGE_REFERENCE)
DEF_ICON_COLOR(IMAGE_RGB)
DEF_ICON_COLOR(IMAGE_RGB_ALPHA)
DEF_ICON_COLOR(IMAGE_ZDEPTH)
DEF_ICON_COLOR(IMASEL) /* Image Selector? Not an editor! */
DEF_ICON_COLOR(IMGDISPLAY)
DEF_ICON_COLOR(IMPORT)
DEF_ICON_COLOR(INCREASE_KERNING)
DEF_ICON_COLOR(INC_CONTRAST)
DEF_ICON_COLOR(INDENT)
DEF_ICON_COLOR(INDEX)
DEF_ICON_COLOR(INDEX_OF_NEAREST)
DEF_ICON_COLOR(INDEX_SWITCH)
DEF_ICON_COLOR(INDIRECT_ONLY_OFF)
DEF_ICON_COLOR(INDIRECT_ONLY_ON)
DEF_ICON_COLOR(INFLATE)
DEF_ICON_COLOR(INFO) /* Info Editor */
DEF_ICON_COLOR(INPUT_BOOL)
DEF_ICON_COLOR(INSET_FACES)
DEF_ICON_COLOR(INSTANCES_TO_POINTS)
DEF_ICON_COLOR(INSTANCE_BOUNDS)
DEF_ICON_COLOR(INSTANCE_ROTATE)
DEF_ICON_COLOR(INSTANCE_SCALE)
DEF_ICON_COLOR(INSTANCE_TRANSFORM)
DEF_ICON_COLOR(INSTANCE_TRANSFORM_GET)
DEF_ICON_COLOR(INTEGER)
DEF_ICON_COLOR(INTEGER_MATH)
DEF_ICON_COLOR(INTERNET)
DEF_ICON_COLOR(INTERNET_OFFLINE)
DEF_ICON_COLOR(INTERPOLATE)
DEF_ICON_COLOR(INTERPOLATE_CURVE)
DEF_ICON_COLOR(INTERSECT)
DEF_ICON_COLOR(INVERSE)
DEF_ICON_COLOR(INVERSESQUARECURVE)
DEF_ICON_COLOR(INVERT_MASK)
DEF_ICON_COLOR(INVERT_MATRIX)
DEF_ICON_COLOR(INVERT_ROTATION)
DEF_ICON_COLOR(IPO) /* Graph Editor */
DEF_ICON_COLOR(IPO_BACK)
DEF_ICON_COLOR(IPO_BEZIER)
DEF_ICON_COLOR(IPO_BOUNCE)
DEF_ICON_COLOR(IPO_CIRC)
DEF_ICON_COLOR(IPO_CONSTANT)
DEF_ICON_COLOR(IPO_CUBIC)
DEF_ICON_COLOR(IPO_EASE_IN)
DEF_ICON_COLOR(IPO_EASE_IN_OUT)
DEF_ICON_COLOR(IPO_EASE_OUT)
DEF_ICON_COLOR(IPO_ELASTIC)
DEF_ICON_COLOR(IPO_EXPO)
DEF_ICON_COLOR(IPO_LINEAR)
DEF_ICON_COLOR(IPO_QUAD)
DEF_ICON_COLOR(IPO_QUART)
DEF_ICON_COLOR(IPO_QUINT)
DEF_ICON_COLOR(IPO_SINE)
DEF_ICON_COLOR(IS_SPLINE_CYCLIC)
DEF_ICON_COLOR(ITALIC)

/* letter J */

DEF_ICON_COLOR(JOIN)
DEF_ICON_COLOR(JOINCOPY)
DEF_ICON_COLOR(JOIN_AREAS)
DEF_ICON_COLOR(JUMP_TO_KEYFRAMES)

/* letter K */

DEF_ICON_COLOR(KEYFRAME)
DEF_ICON_COLOR(KEYFRAMES_CLEAR)
DEF_ICON_COLOR(KEYFRAMES_INSERT)
DEF_ICON_COLOR(KEYFRAMES_REMOVE)
DEF_ICON_COLOR(KEYFRAME_HLT)
DEF_ICON_COLOR(KEYINGSET)
DEF_ICON_COLOR(KEY_DEHLT)
DEF_ICON_COLOR(KEY_HLT)
DEF_ICON_COLOR(KNIFE)
DEF_ICON_COLOR(KNIFE_PROJECT)
DEF_ICON_COLOR(KUWAHARA)

/* letter L */

DEF_ICON_COLOR(LASSO_ADD)
DEF_ICON_COLOR(LASSO_HIDE)
DEF_ICON_COLOR(LASSO_MASK)
DEF_ICON_COLOR(LASSO_SHOW)
DEF_ICON_COLOR(LASSO_TRIM)
DEF_ICON_COLOR(LASTOPERATOR)
DEF_ICON_COLOR(LATTICE_DATA)
DEF_ICON_COLOR(LAYER)
DEF_ICON_COLOR(LAYER_ACTIVE)
DEF_ICON_COLOR(LAYER_ADD)
DEF_ICON_COLOR(LAYER_USED)
DEF_ICON_COLOR(LENS_ANGLE)
DEF_ICON_COLOR(LENS_SCALE)
DEF_ICON_COLOR(LEVELS)
DEF_ICON_COLOR(LIBRARY)
DEF_ICON_COLOR(LIBRARY_DATA_BROKEN)
DEF_ICON_COLOR(LIBRARY_DATA_DIRECT)
DEF_ICON_COLOR(LIBRARY_DATA_INDIRECT)
DEF_ICON_COLOR(LIBRARY_DATA_OVERRIDE)
DEF_ICON_COLOR(LIBRARY_OBJECT)
DEF_ICON_COLOR(LIGHT)
DEF_ICON_COLOR(LIGHTMAPPACK)
DEF_ICON_COLOR(LIGHTPROBE_PLANE)
DEF_ICON_COLOR(LIGHTPROBE_SPHERE)
DEF_ICON_COLOR(LIGHTPROBE_VOLUME)
DEF_ICON_COLOR(LIGHT_AREA)
DEF_ICON_COLOR(LIGHT_DATA)
DEF_ICON_COLOR(LIGHT_HEMI)
DEF_ICON_COLOR(LIGHT_POINT)
DEF_ICON_COLOR(LIGHT_SIZE)
DEF_ICON_COLOR(LIGHT_SPOT)
DEF_ICON_COLOR(LIGHT_STRENGTH)
DEF_ICON_COLOR(LIGHT_SUN)
DEF_ICON_COLOR(LIMIT_MODIFIER)
DEF_ICON_COLOR(LINCURVE)
DEF_ICON_COLOR(LINEART_COLLECTION)
DEF_ICON_COLOR(LINEART_OBJECT)
DEF_ICON_COLOR(LINEART_SCENE)
DEF_ICON_COLOR(LINEAR_GIZMO)
DEF_ICON_COLOR(LINENUMBERS_OFF)
DEF_ICON_COLOR(LINENUMBERS_ON)
DEF_ICON_COLOR(LINE_DATA)
DEF_ICON_COLOR(LINE_HIDE)
DEF_ICON_COLOR(LINE_PROJECT)
DEF_ICON_COLOR(LINE_SHOW)
DEF_ICON_COLOR(LINE_TRIM)
DEF_ICON_COLOR(LINK_AREA)
DEF_ICON_COLOR(LINK_BLEND)
DEF_ICON_COLOR(LINK_DATA)
DEF_ICON_COLOR(LINK_REPLACE)
DEF_ICON_COLOR(LOAD_3DS)
DEF_ICON_COLOR(LOAD_ABC)
DEF_ICON_COLOR(LOAD_BVH)
DEF_ICON_COLOR(LOAD_CSV)
DEF_ICON_COLOR(LOAD_DAE)
DEF_ICON_COLOR(LOAD_FACTORY)
DEF_ICON_COLOR(LOAD_FBX)
DEF_ICON_COLOR(LOAD_GLTF)
DEF_ICON_COLOR(LOAD_OBJ)
DEF_ICON_COLOR(LOAD_PLY)
DEF_ICON_COLOR(LOAD_STL)
DEF_ICON_COLOR(LOAD_SVG)
DEF_ICON_COLOR(LOAD_SVG_GPENCIL)
DEF_ICON_COLOR(LOAD_USD)
DEF_ICON_COLOR(LOAD_X3D)
DEF_ICON_COLOR(LOCKTOACTIVE)
DEF_ICON_COLOR(LOCKTOCENTER)
DEF_ICON_COLOR(LOCK_CLEAR)
DEF_ICON_COLOR(LOCK_ROTATION)
DEF_ICON_COLOR(LOCK_TO_CAMVIEW)
DEF_ICON_COLOR(LOCK_TO_CAMVIEW_ON)
DEF_ICON_COLOR(LOC_ROT)
DEF_ICON_COLOR(LOC_ROT_SCALE)
DEF_ICON_COLOR(LOC_ROT_SCALE_CUSTOM)
DEF_ICON_COLOR(LOC_SCALE)
DEF_ICON_COLOR(LONGDISPLAY)
DEF_ICON_COLOR(LOOPSEL)
DEF_ICON_COLOR(LOOP_BACK)
DEF_ICON_COLOR(LOOP_CUT_AND_SLIDE)
DEF_ICON_COLOR(LOOP_FORWARDS)

/* letter M */

DEF_ICON_COLOR(MAGIC_TEX)
DEF_ICON_COLOR(MAKEDUPLIFACE)
DEF_ICON_COLOR(MAKEDUPLIREAL)
DEF_ICON_COLOR(MAKE_CURVESEGMENT)
DEF_ICON_COLOR(MAKE_EDGEFACE)
DEF_ICON_COLOR(MAKE_INTERNAL)
DEF_ICON_COLOR(MAKE_LOCAL)
DEF_ICON_COLOR(MAKE_PLANAR)
DEF_ICON_COLOR(MAKE_PROXY)
DEF_ICON_COLOR(MAKE_REGULAR)
DEF_ICON_COLOR(MAKE_SCREENSHOT)
DEF_ICON_COLOR(MAKE_SCREENSHOT_AREA)
DEF_ICON_COLOR(MAKE_SINGLE_USER)
DEF_ICON_COLOR(MANIPUL)
DEF_ICON_COLOR(MAN_ROT)
DEF_ICON_COLOR(MAN_SCALE)
DEF_ICON_COLOR(MAN_TRANS)
DEF_ICON_COLOR(MARBLE_TEX)
DEF_ICON_COLOR(MARKER)
DEF_ICON_COLOR(MARKER_BIND)
DEF_ICON_COLOR(MARKER_HLT)
DEF_ICON_COLOR(MARKER_TO_MESH)
DEF_ICON_COLOR(MARKFSFACE)
DEF_ICON_COLOR(MARKSHARPANGLE)
DEF_ICON_COLOR(MARKSHARPEDGES)
DEF_ICON_COLOR(MARKSHARPVERTS)
DEF_ICON_COLOR(MARK_FS_EDGE)
DEF_ICON_COLOR(MARK_SEAM)
DEF_ICON_COLOR(MASK_ABOVE)
DEF_ICON_COLOR(MASK_BELOW)
DEF_ICON_COLOR(MASK_SLICE)
DEF_ICON_COLOR(MASK_SLICE_FILL)
DEF_ICON_COLOR(MASK_SLICE_NEW)
DEF_ICON_COLOR(MATCH_STRING)
DEF_ICON_COLOR(MATCLOTH)
DEF_ICON_COLOR(MATCUBE)
DEF_ICON_COLOR(MATERIAL)
DEF_ICON_COLOR(MATERIAL_ADD)
DEF_ICON_COLOR(MATERIAL_DATA)
DEF_ICON_COLOR(MATERIAL_INDEX)
DEF_ICON_COLOR(MATERIAL_REPLACE)
DEF_ICON_COLOR(MATFLUID)
DEF_ICON_COLOR(MATPLANE)
DEF_ICON_COLOR(MATSHADERBALL)
DEF_ICON_COLOR(MATSPHERE)
DEF_ICON_COLOR(MAT_SPHERE_SKY)
DEF_ICON_COLOR(MATRIX_DETERMINANT)
DEF_ICON_COLOR(MAXIMIZE_AREA)
DEF_ICON_COLOR(MEMORY)
DEF_ICON_COLOR(MENU_PANEL)
DEF_ICON_COLOR(MENU_SWITCH)
DEF_ICON_COLOR(MERGE)
DEF_ICON_COLOR(MERGE_AT_FIRST)
DEF_ICON_COLOR(MERGE_AT_LAST)
DEF_ICON_COLOR(MERGE_CENTER)
DEF_ICON_COLOR(MERGE_CURSOR)
DEF_ICON_COLOR(MESH_CAPSULE)
DEF_ICON_COLOR(MESH_CIRCLE)
DEF_ICON_COLOR(MESH_CONE)
DEF_ICON_COLOR(MESH_CUBE)
DEF_ICON_COLOR(MESH_CYLINDER)
DEF_ICON_COLOR(MESH_DATA)
DEF_ICON_COLOR(MESH_GRID)
DEF_ICON_COLOR(MESH_ICOSPHERE)
DEF_ICON_COLOR(MESH_LINE)
DEF_ICON_COLOR(MESH_MONKEY)
DEF_ICON_COLOR(MESH_PLANE)
DEF_ICON_COLOR(MESH_TORUS)
DEF_ICON_COLOR(MESH_TO_POINTS)
DEF_ICON_COLOR(MESH_TO_VOLUME)
DEF_ICON_COLOR(MESH_UVSPHERE)
DEF_ICON_COLOR(META_BALL)
DEF_ICON_COLOR(META_CAPSULE)
DEF_ICON_COLOR(META_CUBE)
DEF_ICON_COLOR(META_DATA)
DEF_ICON_COLOR(META_ELLIPSOID)
DEF_ICON_COLOR(META_EMPTY)
DEF_ICON_COLOR(META_PLANE)
DEF_ICON_COLOR(METALLIC)
DEF_ICON_COLOR(MINIMIZESTRETCH)
DEF_ICON_COLOR(MINMAX)
DEF_ICON_COLOR(MIRROR_CURSORVALUE)
DEF_ICON_COLOR(MIRROR_MARKER)
DEF_ICON_COLOR(MIRROR_TIME)
DEF_ICON_COLOR(MIRROR_VERTEXGROUP)
DEF_ICON_COLOR(MIRROR_X)
DEF_ICON_COLOR(MIRROR_Y)
DEF_ICON_COLOR(MIRROR_Z)
DEF_ICON_COLOR(MODIFIER)
DEF_ICON_COLOR(MODIFIER_DATA)
DEF_ICON_COLOR(MODIFIER_OFF)
DEF_ICON_COLOR(MODIFIER_ON)
DEF_ICON_COLOR(MOD_ARMATURE)
DEF_ICON_COLOR(MOD_ARMATURE_SELECTED)
DEF_ICON_COLOR(MOD_ARRAY)
DEF_ICON_COLOR(MOD_BEVEL)
DEF_ICON_COLOR(MOD_BOOLEAN)
DEF_ICON_COLOR(MOD_BUILD)
DEF_ICON_COLOR(MOD_CAST)
DEF_ICON_COLOR(MOD_CLOTH)
DEF_ICON_COLOR(MOD_CURVE)
DEF_ICON_COLOR(MOD_DASH)
DEF_ICON_COLOR(MOD_DATA_TRANSFER)
DEF_ICON_COLOR(MOD_DECIM)
DEF_ICON_COLOR(MOD_DISPLACE)
DEF_ICON_COLOR(MOD_DYNAMICPAINT)
DEF_ICON_COLOR(MOD_EDGESPLIT)
DEF_ICON_COLOR(MOD_ENVELOPE)
DEF_ICON_COLOR(MOD_EQUALIZER)
DEF_ICON_COLOR(MOD_EXPLODE)
DEF_ICON_COLOR(MOD_FLUID)
DEF_ICON_COLOR(MOD_FLUIDSIM)
DEF_ICON_COLOR(MOD_HUE_SATURATION)
DEF_ICON_COLOR(MOD_LATTICE)
DEF_ICON_COLOR(MOD_LINEART)
DEF_ICON_COLOR(MOD_MASK)
DEF_ICON_COLOR(MOD_MASK_OFF)
DEF_ICON_COLOR(MOD_MESHDEFORM)
DEF_ICON_COLOR(MOD_MESH_CACHE)
DEF_ICON_COLOR(MOD_MIRROR)
DEF_ICON_COLOR(MOD_MULTIRES)
DEF_ICON_COLOR(MOD_NOISE)
DEF_ICON_COLOR(MOD_NORMALEDIT)
DEF_ICON_COLOR(MOD_OCEAN)
DEF_ICON_COLOR(MOD_OFFSET)
DEF_ICON_COLOR(MOD_OPACITY)
DEF_ICON_COLOR(MOD_OUTLINE)
DEF_ICON_COLOR(MOD_PARTICLES)
DEF_ICON_COLOR(MOD_PARTICLE_INSTANCE)
DEF_ICON_COLOR(MOD_PHYSICS)
DEF_ICON_COLOR(MOD_REMESH)
DEF_ICON_COLOR(MOD_SCREW)
DEF_ICON_COLOR(MOD_SHRINKWRAP)
DEF_ICON_COLOR(MOD_SIMPLEDEFORM)
DEF_ICON_COLOR(MOD_SIMPLIFY)
DEF_ICON_COLOR(MOD_SKIN)
DEF_ICON_COLOR(MOD_SMOKE)
DEF_ICON_COLOR(MOD_SMOOTH)
DEF_ICON_COLOR(MOD_SOFT)
DEF_ICON_COLOR(MOD_SOLIDIFY)
DEF_ICON_COLOR(MOD_SUBSURF)
DEF_ICON_COLOR(MOD_THICKNESS)
DEF_ICON_COLOR(MOD_TIME)
DEF_ICON_COLOR(MOD_TINT)
DEF_ICON_COLOR(MOD_TRIANGULATE)
DEF_ICON_COLOR(MOD_UVPROJECT)
DEF_ICON_COLOR(MOD_VERTEX_WEIGHT)
DEF_ICON_COLOR(MOD_WARP)
DEF_ICON_COLOR(MOD_WAVE)
DEF_ICON_COLOR(MOD_WIREFRAME)
DEF_ICON_COLOR(MONKEY)
DEF_ICON_COLOR(MOTIONPATHS_CALCULATE)
DEF_ICON_COLOR(MOTIONPATHS_CLEAR)
DEF_ICON_COLOR(MOTIONPATHS_UPDATE)
DEF_ICON_COLOR(MOTIONPATHS_UPDATE_ALL)

DEF_ICON_COLOR(MOUSE_LMB)        /*bfa- don't change the order of the mouse icons*/
DEF_ICON_COLOR(MOUSE_MMB)        /*bfa- don't change the order of the mouse icons*/
DEF_ICON_COLOR(MOUSE_RMB)        /*bfa- don't change the order of the mouse icons*/
DEF_ICON_COLOR(MOUSE_MMB_SCROLL) /*bfa- don't change the order of the mouse icons*/
DEF_ICON_COLOR(MOUSE_LMB_2X)     /*bfa- don't change the order of the mouse icons*/
DEF_ICON_COLOR(MOUSE_MOVE)       /*bfa- don't change the order of the mouse icons*/
DEF_ICON_COLOR(MOUSE_LMB_DRAG)   /*bfa- don't change the order of the mouse icons*/
DEF_ICON_COLOR(MOUSE_MMB_DRAG)   /*bfa- don't change the order of the mouse icons*/
DEF_ICON_COLOR(MOUSE_RMB_DRAG)   /*bfa- don't change the order of the mouse icons*/

DEF_ICON_COLOR(MOUSE_POSITION)
DEF_ICON_COLOR(MOVE_DOWN)
DEF_ICON_COLOR(MOVE_TEXTURESPACE)
DEF_ICON_COLOR(MOVE_TO_BOTTOM)
DEF_ICON_COLOR(MOVE_TO_TOP)
DEF_ICON_COLOR(MOVE_UP)
DEF_ICON_COLOR(MULTIPLICATION)
DEF_ICON_COLOR(MULTIPLY_MATRIX)
DEF_ICON_COLOR(MUSGRAVE_TEX)
DEF_ICON_COLOR(MUTE_IPO_OFF)
DEF_ICON_COLOR(MUTE_IPO_ON)

/* letter N */

DEF_ICON_COLOR(NAMED_ATTRIBUTE)
DEF_ICON_COLOR(NAMED_LAYER_SELECTION)
DEF_ICON_COLOR(NETWORK_DRIVE)
DEF_ICON_COLOR(NEW)
DEF_ICON_COLOR(NEWFOLDER)
DEF_ICON_COLOR(PREVIEW_LOADING)
DEF_ICON_COLOR(NEW_GROUP)
DEF_ICON_COLOR(NEW_WINDOW)
DEF_ICON_COLOR(NEW_WINDOW_MAIN)
DEF_ICON_COLOR(NEXTACTIVE)
DEF_ICON_COLOR(NEXT_KEYFRAME)
DEF_ICON_COLOR(NLA) /* NLA Editor */
DEF_ICON_COLOR(NLA_ACTIVE)
DEF_ICON_COLOR(NLA_PUSHDOWN)
DEF_ICON_COLOR(NOCURVE)
DEF_ICON_COLOR(NODE)
DEF_ICON_COLOR(NODETREE)
DEF_ICON_COLOR(NODETREE_ACTIVE)
DEF_ICON_COLOR(NODE_ADD_SHADER)
DEF_ICON_COLOR(NODE_ALPHACONVERT)
DEF_ICON_COLOR(NODE_AMBIENT_OCCLUSION)
DEF_ICON_COLOR(NODE_ANISOTOPIC)
DEF_ICON_COLOR(NODE_AT)
DEF_ICON_COLOR(NODE_ATTRIBUTE)
DEF_ICON_COLOR(NODE_BACKGROUNDSHADER)
DEF_ICON_COLOR(NODE_BILATERAL_BLUR)
DEF_ICON_COLOR(NODE_BLACKBODY)
DEF_ICON_COLOR(NODE_BLUR)
DEF_ICON_COLOR(NODE_BOKEH_BLUR)
DEF_ICON_COLOR(NODE_BOKEH_IMAGE)
DEF_ICON_COLOR(NODE_BOXMASK)
DEF_ICON_COLOR(NODE_BRICK)
DEF_ICON_COLOR(NODE_BUMP)
DEF_ICON_COLOR(NODE_CHANNEL)
DEF_ICON_COLOR(NODE_CHECKER)
DEF_ICON_COLOR(NODE_CHROMA)
DEF_ICON_COLOR(NODE_CLAMP)
DEF_ICON_COLOR(NODE_CLOUDS)
DEF_ICON_COLOR(NODE_COLORBALANCE)
DEF_ICON_COLOR(NODE_COLORCORRECTION)
DEF_ICON_COLOR(NODE_COLORRAMP)
DEF_ICON_COLOR(NODE_COMBINEHSV)
DEF_ICON_COLOR(NODE_COMBINERGB)
DEF_ICON_COLOR(NODE_COMBINEXYZ)
DEF_ICON_COLOR(NODE_COMBINEYCBCRA)
DEF_ICON_COLOR(NODE_COMBINEYUVA)
DEF_ICON_COLOR(NODE_COMPOSITING)
DEF_ICON_COLOR(NODE_COMPOSITING_ACTIVE)
DEF_ICON_COLOR(NODE_CORNER)
DEF_ICON_COLOR(NODE_CORNERPIN)
DEF_ICON_COLOR(NODE_CROP)
DEF_ICON_COLOR(NODE_CURVE_TIME)
DEF_ICON_COLOR(NODE_DEFOCUS)
DEF_ICON_COLOR(NODE_DENOISE)
DEF_ICON_COLOR(NODE_DESPECKLE)
DEF_ICON_COLOR(NODE_DIFFUSESHADER)
DEF_ICON_COLOR(NODE_DIRECITONALBLUR)
DEF_ICON_COLOR(NODE_DOUBLEEDGEMASK)
DEF_ICON_COLOR(NODE_EDITGROUP)
DEF_ICON_COLOR(NODE_ELLIPSEMASK)
DEF_ICON_COLOR(NODE_EMISSION)
DEF_ICON_COLOR(NODE_ENVIRONMENT)
DEF_ICON_COLOR(NODE_ERODE)
DEF_ICON_COLOR(NODE_FILEOUTPUT)
DEF_ICON_COLOR(NODE_FRAME)
DEF_ICON_COLOR(NODE_FRESNEL)
DEF_ICON_COLOR(NODE_GAMMA)
DEF_ICON_COLOR(NODE_GEOMETRY)
DEF_ICON_COLOR(NODE_GLARE)
DEF_ICON_COLOR(NODE_GLASSHADER)
DEF_ICON_COLOR(NODE_GLOSSYSHADER)
DEF_ICON_COLOR(NODE_GRADIENT)
DEF_ICON_COLOR(NODE_GROUPINSERT)
DEF_ICON_COLOR(NODE_HAIRINFO)
DEF_ICON_COLOR(NODE_HOLDOUTSHADER)
DEF_ICON_COLOR(NODE_HUESATURATION)
DEF_ICON_COLOR(NODE_IMPAINT)

DEF_ICON_COLOR(NODE_INSERT_ON) /*bfa - don't change the order of the icons*/
DEF_ICON_COLOR(NODE_INSERT_OFF)/*bfa - don't change the order of the icons*/

DEF_ICON_COLOR(NODE_INVERT)
DEF_ICON_COLOR(NODE_KEYING)
DEF_ICON_COLOR(NODE_KEYINGSCREEN)
DEF_ICON_COLOR(NODE_LAYERWEIGHT)
DEF_ICON_COLOR(NODE_LENSDISTORT)
DEF_ICON_COLOR(NODE_LIGHTFALLOFF)
DEF_ICON_COLOR(NODE_LIGHTPATH)
DEF_ICON_COLOR(NODE_LINESTYLE_OUTPUT)
DEF_ICON_COLOR(NODE_LUMINANCE)
DEF_ICON_COLOR(NODE_MAKEGROUP)
DEF_ICON_COLOR(NODE_MAPPING)
DEF_ICON_COLOR(NODE_MAP_RANGE)
DEF_ICON_COLOR(NODE_MATERIAL)
DEF_ICON_COLOR(NODE_MATH)
DEF_ICON_COLOR(NODE_MIX)
DEF_ICON_COLOR(NODE_MIXRGB)
DEF_ICON_COLOR(NODE_MIXSHADER)
DEF_ICON_COLOR(NODE_MOVIEDISTORT)
DEF_ICON_COLOR(NODE_NORMALIZE)
DEF_ICON_COLOR(NODE_NORMALMAP)
DEF_ICON_COLOR(NODE_OBJECTINFO)
DEF_ICON_COLOR(NODE_OUTPUT)
DEF_ICON_COLOR(NODE_PARTICLEINFO)
DEF_ICON_COLOR(NODE_PIXELATED)
DEF_ICON_COLOR(NODE_PLANETRACKDEFORM)
DEF_ICON_COLOR(NODE_POINTCLOUD)
DEF_ICON_COLOR(NODE_PRINCIPLED)
DEF_ICON_COLOR(NODE_RANGE)
DEF_ICON_COLOR(NODE_REFRACTIONSHADER)
DEF_ICON_COLOR(NODE_RELATIVE_TO_PIXEL)
DEF_ICON_COLOR(NODE_REROUTE)
DEF_ICON_COLOR(NODE_RGB)
DEF_ICON_COLOR(NODE_RGBCURVE)
DEF_ICON_COLOR(NODE_RGBTOBW)
DEF_ICON_COLOR(NODE_SEL)
DEF_ICON_COLOR(NODE_SEPARATEHSV)
DEF_ICON_COLOR(NODE_SEPARATERGB)
DEF_ICON_COLOR(NODE_SEPARATEXYZ)
DEF_ICON_COLOR(NODE_SEPARATE_YCBCRA)
DEF_ICON_COLOR(NODE_SEPARATE_YUVA)
DEF_ICON_COLOR(NODE_SIDE)
DEF_ICON_COLOR(NODE_SKY)
DEF_ICON_COLOR(NODE_SPILL)
DEF_ICON_COLOR(NODE_SSS)
DEF_ICON_COLOR(NODE_STABILIZE2D)
DEF_ICON_COLOR(NODE_SUNBEAMS)
DEF_ICON_COLOR(NODE_TANGENT)
DEF_ICON_COLOR(NODE_TEXCOORDINATE)
DEF_ICON_COLOR(NODE_TONEMAP)
DEF_ICON_COLOR(NODE_TOONSHADER)
DEF_ICON_COLOR(NODE_TOP)
DEF_ICON_COLOR(NODE_TRACKPOSITION)
DEF_ICON_COLOR(NODE_TRANSFORM)
DEF_ICON_COLOR(NODE_TRANSFORM_CLEAR)
DEF_ICON_COLOR(NODE_TRANSLUCENT)
DEF_ICON_COLOR(NODE_TRANSPARENT)
DEF_ICON_COLOR(NODE_UNGROUP)
DEF_ICON_COLOR(NODE_UVALONGSTROKE)
DEF_ICON_COLOR(NODE_VALUE)
DEF_ICON_COLOR(NODE_VECTOR)
DEF_ICON_COLOR(NODE_VECTORMATH)
DEF_ICON_COLOR(NODE_VECTORROTATE)
DEF_ICON_COLOR(NODE_VECTOR_BLUR)
DEF_ICON_COLOR(NODE_VECTOR_TRANSFORM)
DEF_ICON_COLOR(NODE_VELVET)
DEF_ICON_COLOR(NODE_VERTEX_COLOR)
DEF_ICON_COLOR(NODE_VIEWER)
DEF_ICON_COLOR(NODE_VIWERSPLIT)
DEF_ICON_COLOR(NODE_VOLUMEABSORPTION)
DEF_ICON_COLOR(NODE_VOLUMEPRINCIPLED)
DEF_ICON_COLOR(NODE_VOLUMESCATTER)
DEF_ICON_COLOR(NODE_VOLUME_INFO)
DEF_ICON_COLOR(NODE_WAVELENGTH)
DEF_ICON_COLOR(NODE_WAVES)
DEF_ICON_COLOR(NODE_WHITE_NOISE)
DEF_ICON_COLOR(NODE_WIREFRAME)
DEF_ICON_COLOR(NODE_ZCOMBINE)
DEF_ICON_COLOR(NOISE)
DEF_ICON_COLOR(NOISE_MODIFIER)
DEF_ICON_COLOR(NOISE_TEX)
DEF_ICON_COLOR(NORMALIZE_FCURVES)
DEF_ICON_COLOR(NORMALS_FACE)
DEF_ICON_COLOR(NORMALS_VERTEX)
DEF_ICON_COLOR(NORMALS_VERTEX_FACE)
DEF_ICON_COLOR(NORMAL_AVERAGE)
DEF_ICON_COLOR(NORMAL_MULTIPLY)
DEF_ICON_COLOR(NORMAL_ROTATE)
DEF_ICON_COLOR(NORMAL_SETSTRENGTH)
DEF_ICON_COLOR(NORMAL_SMOOTH)
DEF_ICON_COLOR(NORMAL_TARGET)

/* letter O */

DEF_ICON_COLOR(OBJECT_CONTENTS)
DEF_ICON_COLOR(OBJECT_DATA)
DEF_ICON_COLOR(OBJECT_DATAMODE)
DEF_ICON_COLOR(OBJECT_HIDDEN)
DEF_ICON_COLOR(OBJECT_ORIGIN)
DEF_ICON_COLOR(OFFSET_CORNER_IN_FACE)
DEF_ICON_COLOR(OFFSET_EDGE_SLIDE)
DEF_ICON_COLOR(OFFSET_POINT_IN_CURVE)
DEF_ICON_COLOR(ONIONSKIN_OFF)
DEF_ICON_COLOR(ONIONSKIN_ON)
DEF_ICON_COLOR(OOPS)        /* Outliner */
DEF_ICON_COLOR(OOPS_ACTIVE) /* Outliner */
DEF_ICON_COLOR(OPEN_RECENT)
DEF_ICON_COLOR(OPTIMIZE)
DEF_ICON_COLOR(OPTIONS)
DEF_ICON_COLOR(ORBIT_DOWN)
DEF_ICON_COLOR(ORBIT_LEFT)
DEF_ICON_COLOR(ORBIT_OPPOSITE)
DEF_ICON_COLOR(ORBIT_RIGHT)
DEF_ICON_COLOR(ORBIT_UP)
DEF_ICON_COLOR(ORIENTATION_CURSOR)
DEF_ICON_COLOR(ORIENTATION_GIMBAL)
DEF_ICON_COLOR(ORIENTATION_GLOBAL)
DEF_ICON_COLOR(ORIENTATION_LOCAL)
DEF_ICON_COLOR(ORIENTATION_NORMAL)
DEF_ICON_COLOR(ORIENTATION_PARENT)
DEF_ICON_COLOR(ORIENTATION_VIEW)
DEF_ICON_COLOR(ORIGIN)
DEF_ICON_COLOR(ORIGIN_TO_CENTEROFMASS)
DEF_ICON_COLOR(ORIGIN_TO_CURSOR)
DEF_ICON_COLOR(ORIGIN_TO_GEOMETRY)
DEF_ICON_COLOR(ORIGIN_TO_VOLUME)
DEF_ICON_COLOR(ORPHAN_DATA)
DEF_ICON_COLOR(ORTHO)
DEF_ICON_COLOR(OUTLINER)
DEF_ICON_COLOR(OUTLINER_COLLECTION)
DEF_ICON_COLOR(OUTLINER_DATA_ARMATURE)
DEF_ICON_COLOR(OUTLINER_DATA_CAMERA)
DEF_ICON_COLOR(OUTLINER_DATA_CURVE)
DEF_ICON_COLOR(OUTLINER_DATA_EMPTY)
DEF_ICON_COLOR(OUTLINER_DATA_FONT)
DEF_ICON_COLOR(OUTLINER_DATA_GP_LAYER)
DEF_ICON_COLOR(OUTLINER_DATA_GREASEPENCIL)
DEF_ICON_COLOR(OUTLINER_DATA_LATTICE)
DEF_ICON_COLOR(OUTLINER_DATA_LIGHT)
DEF_ICON_COLOR(OUTLINER_DATA_LIGHTPROBE)
DEF_ICON_COLOR(OUTLINER_DATA_MESH)
DEF_ICON_COLOR(OUTLINER_DATA_META)
DEF_ICON_COLOR(OUTLINER_DATA_POSE)
DEF_ICON_COLOR(OUTLINER_DATA_SPEAKER)
DEF_ICON_COLOR(OUTLINER_DATA_SURFACE)
DEF_ICON_COLOR(OUTLINER_DATA_VOLUME)
DEF_ICON_COLOR(OUTLINER_OB_ARMATURE)
DEF_ICON_COLOR(OUTLINER_OB_CAMERA)
DEF_ICON_COLOR(OUTLINER_OB_CURVE)
DEF_ICON_COLOR(OUTLINER_OB_CURVES)
DEF_ICON_COLOR(OUTLINER_OB_EMPTY)
DEF_ICON_COLOR(OUTLINER_OB_FONT)
DEF_ICON_COLOR(OUTLINER_OB_FORCE_FIELD)
DEF_ICON_COLOR(OUTLINER_OB_GREASEPENCIL)
DEF_ICON_COLOR(OUTLINER_OB_GROUP_INSTANCE)
DEF_ICON_COLOR(OUTLINER_OB_IMAGE)
DEF_ICON_COLOR(OUTLINER_OB_LATTICE)
DEF_ICON_COLOR(OUTLINER_OB_LIGHT)
DEF_ICON_COLOR(OUTLINER_OB_LIGHTPROBE)
DEF_ICON_COLOR(OUTLINER_OB_MESH)
DEF_ICON_COLOR(OUTLINER_OB_META)
DEF_ICON_COLOR(OUTLINER_OB_POINTCLOUD)
DEF_ICON_COLOR(OUTLINER_OB_SPEAKER)
DEF_ICON_COLOR(OUTLINER_OB_SURFACE)
DEF_ICON_COLOR(OUTLINER_OB_VOLUME)
DEF_ICON_COLOR(OUTPUT)
DEF_ICON_COLOR(OVERLAP)
DEF_ICON_COLOR(OVERLAY)

/* letter P */

DEF_ICON_COLOR(PACKAGE)
DEF_ICON_COLOR(PACKISLAND)
DEF_ICON_COLOR(PAINT_ADD)
DEF_ICON_COLOR(PAINT_AVERAGE)
DEF_ICON_COLOR(PAINT_BLUR)
DEF_ICON_COLOR(PAINT_DARKEN)
DEF_ICON_COLOR(PAINT_DRAW)
DEF_ICON_COLOR(PAINT_LIGHTEN)
DEF_ICON_COLOR(PAINT_MIX)
DEF_ICON_COLOR(PAINT_MULTIPLY)
DEF_ICON_COLOR(PAINT_SMEAR)
DEF_ICON_COLOR(PAINT_SUBTRACT)
DEF_ICON_COLOR(PALETTE)
DEF_ICON_COLOR(PANEL_CLOSE)
DEF_ICON_COLOR(PAN_DOWN)
DEF_ICON_COLOR(PAN_LEFT)
DEF_ICON_COLOR(PAN_RIGHT)
DEF_ICON_COLOR(PAN_UP)
DEF_ICON_COLOR(PARENT)
DEF_ICON_COLOR(PARENT_BONE)
DEF_ICON_COLOR(PARENT_CLEAR)
DEF_ICON_COLOR(PARENT_CURVE)
DEF_ICON_COLOR(PARENT_LATTICE)
DEF_ICON_COLOR(PARENT_OBJECT)
DEF_ICON_COLOR(PARENT_SET)
DEF_ICON_COLOR(PARTICLEBRUSH_ADD)
DEF_ICON_COLOR(PARTICLEBRUSH_COMB)
DEF_ICON_COLOR(PARTICLEBRUSH_LENGTH)
DEF_ICON_COLOR(PARTICLEBRUSH_NONE)
DEF_ICON_COLOR(PARTICLEBRUSH_PUFF)
DEF_ICON_COLOR(PARTICLEBRUSH_SMOOTH)
DEF_ICON_COLOR(PARTICLEBRUSH_WEIGHT)
DEF_ICON_COLOR(PARTICLEMODE)
DEF_ICON_COLOR(PARTICLES)
DEF_ICON_COLOR(PARTICLE_DATA)
DEF_ICON_COLOR(PARTICLE_PATH)
DEF_ICON_COLOR(PARTICLE_POINT)
DEF_ICON_COLOR(PARTICLE_TIP)
DEF_ICON_COLOR(PASS)
DEF_ICON_COLOR(PASTEDOWN)
DEF_ICON_COLOR(PASTEFILE)
DEF_ICON_COLOR(PASTEFLIPDOWN)
DEF_ICON_COLOR(PASTEFLIPUP)
DEF_ICON_COLOR(PATTERN)
DEF_ICON_COLOR(PAUSE)
DEF_ICON_COLOR(PERIMETER)
DEF_ICON_COLOR(PERSP_ORTHO)
DEF_ICON_COLOR(PHYSICS)
DEF_ICON_COLOR(PIVOT_ACTIVE)
DEF_ICON_COLOR(PIVOT_BOUNDBOX)
DEF_ICON_COLOR(PIVOT_CURSOR)
DEF_ICON_COLOR(PIVOT_INDIVIDUAL)
DEF_ICON_COLOR(PIVOT_MEDIAN)
DEF_ICON_COLOR(PIVOT_TO_ACTIVE_VERT)
DEF_ICON_COLOR(PIVOT_TO_MASKBORDER)
DEF_ICON_COLOR(PIVOT_TO_ORIGIN)
DEF_ICON_COLOR(PIVOT_TO_SURFACE)
DEF_ICON_COLOR(PIVOT_TO_UNMASKED)
DEF_ICON_COLOR(PLANAR)
DEF_ICON_COLOR(PLANETRACK)
DEF_ICON_COLOR(PLAY)
DEF_ICON_COLOR(PLAY_AUDIO)
DEF_ICON_COLOR(PLAY_REVERSE)
DEF_ICON_COLOR(PLUGIN)
DEF_ICON_COLOR(PLUS)
DEF_ICON_COLOR(PMARKER)
DEF_ICON_COLOR(PMARKER_ACT)
DEF_ICON_COLOR(PMARKER_SEL)
DEF_ICON_COLOR(POINTCLOUD_DATA)
DEF_ICON_COLOR(POINTCLOUD_POINT)
DEF_ICON_COLOR(POINTS_TO_CURVES)
DEF_ICON_COLOR(POINTS_TO_VERTICES)
DEF_ICON_COLOR(POINT_DISTRIBUTE)
DEF_ICON_COLOR(POINT_INFO)
DEF_ICON_COLOR(POINT_INSTANCE)
DEF_ICON_COLOR(POINT_OF_CURVE)
DEF_ICON_COLOR(POINT_ROTATE)
DEF_ICON_COLOR(POINT_SCALE)
DEF_ICON_COLOR(POINT_SEPARATE)
DEF_ICON_COLOR(POINT_TO_VOLUME)
DEF_ICON_COLOR(POINT_TRANSLATE)
DEF_ICON_COLOR(POKEFACES)
DEF_ICON_COLOR(POLE)
DEF_ICON_COLOR(POLYGONSIDES)
DEF_ICON_COLOR(POLYLINE_HIDE)
DEF_ICON_COLOR(POLYLINE_SHOW)
DEF_ICON_COLOR(POSE_DATA)
DEF_ICON_COLOR(POSE_FROM_BREAKDOWN)
DEF_ICON_COLOR(POSE_HLT)
DEF_ICON_COLOR(POSE_RELAX_TO_BREAKDOWN)
DEF_ICON_COLOR(POSITION)
DEF_ICON_COLOR(POSTERIZE)
DEF_ICON_COLOR(POUND)
DEF_ICON_COLOR(PREFERENCES)
DEF_ICON_COLOR(PREFETCH)
DEF_ICON_COLOR(PREFIX)
DEF_ICON_COLOR(PRESET)
DEF_ICON_COLOR(PREVIEW_RANGE)
DEF_ICON_COLOR(PREVIOUSACTIVE)
DEF_ICON_COLOR(PREV_KEYFRAME)
DEF_ICON_COLOR(PROJECTFROMVIEW)
DEF_ICON_COLOR(PROJECTFROMVIEW_BOUNDS)
DEF_ICON_COLOR(PROJECT_POINT)
DEF_ICON_COLOR(PROMILLE)
DEF_ICON_COLOR(PROPAGATE)
DEF_ICON_COLOR(PROPAGATE_MARKER)
DEF_ICON_COLOR(PROPAGATE_NEXT)
DEF_ICON_COLOR(PROPAGATE_PREVIOUS)
DEF_ICON_COLOR(PROPAGATE_SELECTED)
DEF_ICON_COLOR(PROPERTIES)

DEF_ICON_COLOR(PROP_OFF) /*bfa- don't change the order of the prop icons*/
DEF_ICON_COLOR(PROP_ON)  /*bfa- don't change the order of the prop icons*/
DEF_ICON_COLOR(PROP_CON) /*bfa- don't change the order of the prop icons*/

DEF_ICON_COLOR(PROP_PROJECTED)
DEF_ICON_COLOR(PROTECT)
DEF_ICON_COLOR(PUSH_POSE)
DEF_ICON_COLOR(PUSH_PULL)

/* letter Q */

DEF_ICON_COLOR(QUADVIEW)
DEF_ICON_COLOR(QUATERNION_TO_ROTATION)
DEF_ICON_COLOR(QUESTION)
DEF_ICON_COLOR(QUIT)

/* letter R */

DEF_ICON_COLOR(RADIO)
DEF_ICON_COLOR(RADIOBUT_OFF)
DEF_ICON_COLOR(RADIOBUT_ON)
DEF_ICON_COLOR(RADIUS)
DEF_ICON_COLOR(RANDOMIZE)
DEF_ICON_COLOR(RANDOMIZE_TRANSFORM)
DEF_ICON_COLOR(RANDOM_FLOAT)
DEF_ICON_COLOR(RAYCAST)
DEF_ICON_COLOR(REC)
DEF_ICON_COLOR(RECALC_NORMALS)
DEF_ICON_COLOR(RECALC_NORMALS_INSIDE)
DEF_ICON_COLOR(RECORD_OFF)
DEF_ICON_COLOR(RECORD_ON)
DEF_ICON_COLOR(RECOVER_AUTO)
DEF_ICON_COLOR(RECOVER_LAST)
DEF_ICON_COLOR(REDO)
DEF_ICON_COLOR(REDO_HISTORY)
DEF_ICON_COLOR(RELATIVEPATH)
DEF_ICON_COLOR(RELAX_FACE_SETS)
DEF_ICON_COLOR(RELAX_POSE)
DEF_ICON_COLOR(RELAX_TOPOLOGY)
DEF_ICON_COLOR(REMOVE)
DEF_ICON_COLOR(REMOVE_ACTIVE_GROUP)
DEF_ICON_COLOR(REMOVE_ALL_GROUPS)
DEF_ICON_COLOR(REMOVE_DOUBLES)
DEF_ICON_COLOR(REMOVE_FROM_ALL_GROUPS)
DEF_ICON_COLOR(REMOVE_METASTRIP)
DEF_ICON_COLOR(REMOVE_SELECTED_FROM_ACTIVE_GROUP)
DEF_ICON_COLOR(RENAME)
DEF_ICON_COLOR(RENAME_X)
DEF_ICON_COLOR(RENAME_Y)
DEF_ICON_COLOR(RENAME_Z)
DEF_ICON_COLOR(RENDERBORDER)
DEF_ICON_COLOR(RENDERBORDER_CLEAR)
DEF_ICON_COLOR(RENDERLAYERS)
DEF_ICON_COLOR(RENDER_ANIMATION)
DEF_ICON_COLOR(RENDER_ANI_VIEW)
DEF_ICON_COLOR(RENDER_REGION)
DEF_ICON_COLOR(RENDER_RESULT)
DEF_ICON_COLOR(RENDER_STILL)
DEF_ICON_COLOR(RENDER_STILL_VIEW)
DEF_ICON_COLOR(REPEAT)
DEF_ICON_COLOR(REPLACE_STRING)
DEF_ICON_COLOR(REPROJECT)
DEF_ICON_COLOR(RESET)

DEF_ICON_COLOR(RESTRICT_COLOR_ON) /*bfa - don't change the order of the icons*/
DEF_ICON_COLOR(RESTRICT_COLOR_OFF)/*bfa - don't change the order of the icons*/
DEF_ICON_COLOR(RESTRICT_RENDER_ON)/*bfa - don't change the order of the icons*/
DEF_ICON_COLOR(RESTRICT_RENDER_OFF)/*bfa - don't change the order of the icons*/
DEF_ICON_COLOR(RESTRICT_SELECT_ON)/*bfa - don't change the order of the icons*/
DEF_ICON_COLOR(RESTRICT_SELECT_OFF)/*bfa - don't change the order of the icons*/
DEF_ICON_COLOR(RESTRICT_VIEW_ON)/*bfa - don't change the order of the icons*/
DEF_ICON_COLOR(RESTRICT_VIEW_OFF)/*bfa - don't change the order of the icons*/

DEF_ICON_COLOR(RETOPO)
DEF_ICON_COLOR(REVERSE_COLORS)
DEF_ICON_COLOR(REVERSE_UVS)
DEF_ICON_COLOR(REW)
DEF_ICON_COLOR(RIGHTARROW)
DEF_ICON_COLOR(RIGHTARROW_THIN)
DEF_ICON_COLOR(RIGID_ADD_ACTIVE)
DEF_ICON_COLOR(RIGID_ADD_PASSIVE)
DEF_ICON_COLOR(RIGID_APPLY_TRANS)
DEF_ICON_COLOR(RIGID_BAKE_TO_KEYFRAME)
DEF_ICON_COLOR(RIGID_BODY)
DEF_ICON_COLOR(RIGID_BODY_CONSTRAINT)
DEF_ICON_COLOR(RIGID_CALCULATE_MASS)
DEF_ICON_COLOR(RIGID_CHANGE_SHAPE)
DEF_ICON_COLOR(RIGID_CONSTRAINTS_CONNECT)
DEF_ICON_COLOR(RIGID_COPY_FROM_ACTIVE)
DEF_ICON_COLOR(RIGID_REMOVE)
DEF_ICON_COLOR(RIMLIGHT)
DEF_ICON_COLOR(RIP)
DEF_ICON_COLOR(RIP_FILL)
DEF_ICON_COLOR(RNA)
DEF_ICON_COLOR(RNDCURVE)
DEF_ICON_COLOR(ROLL_LEFT)
DEF_ICON_COLOR(ROLL_RIGHT)
DEF_ICON_COLOR(ROLL_X_NEG)
DEF_ICON_COLOR(ROLL_X_POS)
DEF_ICON_COLOR(ROLL_X_TANG_NEG)
DEF_ICON_COLOR(ROLL_X_TANG_POS)
DEF_ICON_COLOR(ROLL_Y_NEG)
DEF_ICON_COLOR(ROLL_Y_POS)
DEF_ICON_COLOR(ROLL_Z_NEG)
DEF_ICON_COLOR(ROLL_Z_POS)
DEF_ICON_COLOR(ROLL_Z_TANG_NEG)
DEF_ICON_COLOR(ROLL_Z_TANG_POS)
DEF_ICON_COLOR(ROOTCURVE)
DEF_ICON_COLOR(ROTACTIVE)
DEF_ICON_COLOR(ROTATE)
DEF_ICON_COLOR(ROTATECCW)
DEF_ICON_COLOR(ROTATECENTER)
DEF_ICON_COLOR(ROTATECOLLECTION)
DEF_ICON_COLOR(ROTATECW)
DEF_ICON_COLOR(ROTATE_COLORS)
DEF_ICON_COLOR(ROTATE_EULER)
DEF_ICON_COLOR(ROTATE_INSTANCE)
DEF_ICON_COLOR(ROTATE_MINUS_90)
DEF_ICON_COLOR(ROTATE_PLUS_90)
DEF_ICON_COLOR(ROTATE_UVS)
DEF_ICON_COLOR(ROTATION)
DEF_ICON_COLOR(ROTATION_TO_AXIS_ANGLE)
DEF_ICON_COLOR(ROTATION_TO_EULER)
DEF_ICON_COLOR(ROTATION_TO_QUATERNION)
DEF_ICON_COLOR(ROT_SCALE)
DEF_ICON_COLOR(RULER)

/* letter S */

DEF_ICON_COLOR(SAMPLE_INDEX)
DEF_ICON_COLOR(SAMPLE_KEYFRAMES)
DEF_ICON_COLOR(SAMPLE_NEAREST)
DEF_ICON_COLOR(SAMPLE_NEAREST_SURFACE)
DEF_ICON_COLOR(SAMPLE_UV_SURFACE)
DEF_ICON_COLOR(SATURATION)
DEF_ICON_COLOR(SAVE_3DS)
DEF_ICON_COLOR(SAVE_ABC)
DEF_ICON_COLOR(SAVE_ALL)
DEF_ICON_COLOR(SAVE_AS)
DEF_ICON_COLOR(SAVE_BVH)
DEF_ICON_COLOR(SAVE_COPY)
DEF_ICON_COLOR(SAVE_DAE)
DEF_ICON_COLOR(SAVE_FBX)
DEF_ICON_COLOR(SAVE_GLTF)
DEF_ICON_COLOR(SAVE_OBJ)
DEF_ICON_COLOR(SAVE_PDF)
DEF_ICON_COLOR(SAVE_PLY)
DEF_ICON_COLOR(SAVE_PREFS)
DEF_ICON_COLOR(SAVE_STL)
DEF_ICON_COLOR(SAVE_SVG)
DEF_ICON_COLOR(SAVE_USD)
DEF_ICON_COLOR(SAVE_X3D)
DEF_ICON_COLOR(SCALE_AVERAGE)
DEF_ICON_COLOR(SCALE_INSTANCE)
DEF_ICON_COLOR(SCALE_TEXTURESPACE)
DEF_ICON_COLOR(SCENE)
DEF_ICON_COLOR(SCENE_DATA)
DEF_ICON_COLOR(SCREEN_BACK)
DEF_ICON_COLOR(SCREW)
DEF_ICON_COLOR(SCRIPT)
DEF_ICON_COLOR(SCRIPTPLUGINS)
DEF_ICON_COLOR(SCRIPTWIN)
DEF_ICON_COLOR(SCULPTMODE_HLT)
DEF_ICON_COLOR(SCULPT_DYNTOPO)
DEF_ICON_COLOR(SEAMSFROMISLAND)
DEF_ICON_COLOR(SEARCH_MENU)
DEF_ICON_COLOR(SELECTIONTOACTIVE)
DEF_ICON_COLOR(SELECTIONTOCURSOR)
DEF_ICON_COLOR(SELECTIONTOCURSOROFFSET)
DEF_ICON_COLOR(SELECTIONTOGRID)
DEF_ICON_COLOR(SELECTLESS)
DEF_ICON_COLOR(SELECTMORE)
DEF_ICON_COLOR(SELECT_ALL)
DEF_ICON_COLOR(SELECT_BOUNDARY)
DEF_ICON_COLOR(SELECT_BY_MATERIAL)
DEF_ICON_COLOR(SELECT_DIFFERENCE)
DEF_ICON_COLOR(SELECT_EDGELOOP)
DEF_ICON_COLOR(SELECT_EDGERING)
DEF_ICON_COLOR(SELECT_EXTEND)
DEF_ICON_COLOR(SELECT_FACES_BY_SIDE)
DEF_ICON_COLOR(SELECT_FIRST)
DEF_ICON_COLOR(SELECT_HANDLETYPE)
DEF_ICON_COLOR(SELECT_HANDLE_BOTH)
DEF_ICON_COLOR(SELECT_HANDLE_LEFT)
DEF_ICON_COLOR(SELECT_HANDLE_RIGHT)
DEF_ICON_COLOR(SELECT_INTERIOR)
DEF_ICON_COLOR(SELECT_INTERSECT)
DEF_ICON_COLOR(SELECT_KEY)
DEF_ICON_COLOR(SELECT_LAST)
DEF_ICON_COLOR(SELECT_LINE)
DEF_ICON_COLOR(SELECT_LOOPINNER)
DEF_ICON_COLOR(SELECT_LOOSE)
DEF_ICON_COLOR(SELECT_NONE)
DEF_ICON_COLOR(SELECT_NONMANIFOLD)
DEF_ICON_COLOR(SELECT_ROOT)
DEF_ICON_COLOR(SELECT_SET)
DEF_ICON_COLOR(SELECT_SHARPEDGES)
DEF_ICON_COLOR(SELECT_SHORTESTPATH)
DEF_ICON_COLOR(SELECT_SIDEOFACTIVE)
DEF_ICON_COLOR(SELECT_SUBTRACT)
DEF_ICON_COLOR(SELECT_TIP)
DEF_ICON_COLOR(SELECT_TRACKS)
DEF_ICON_COLOR(SELECT_UNGROUPED_VERTS)
DEF_ICON_COLOR(SELF_OBJECT)
DEF_ICON_COLOR(SEPARATE)
DEF_ICON_COLOR(SEPARATE_BYMATERIAL)
DEF_ICON_COLOR(SEPARATE_COLOR)
DEF_ICON_COLOR(SEPARATE_COPY)
DEF_ICON_COLOR(SEPARATE_GEOMETRY)
DEF_ICON_COLOR(SEPARATE_GP_LAYER)
DEF_ICON_COLOR(SEPARATE_GP_POINTS)
DEF_ICON_COLOR(SEPARATE_GP_STROKES)
DEF_ICON_COLOR(SEPARATE_LOOSE)
DEF_ICON_COLOR(SEPARATE_MATRIX)
DEF_ICON_COLOR(SEPARATE_TRANSFORM)
DEF_ICON_COLOR(SEQUENCE) /* Sequencer */
DEF_ICON_COLOR(SEQ_ADD)
DEF_ICON_COLOR(SEQ_ALPHA_OVER)
DEF_ICON_COLOR(SEQ_CHROMA_SCOPE)
DEF_ICON_COLOR(SEQ_CLEAR_OFFSET)
DEF_ICON_COLOR(SEQ_DEINTERLACE)
DEF_ICON_COLOR(SEQ_HISTOGRAM)
DEF_ICON_COLOR(SEQ_INSERT_GAPS)
DEF_ICON_COLOR(SEQ_LUMA_WAVEFORM)
DEF_ICON_COLOR(SEQ_MOVE_EXTEND)
DEF_ICON_COLOR(SEQ_MULTICAM)
DEF_ICON_COLOR(SEQ_MULTIPLY)
DEF_ICON_COLOR(SEQ_PREVIEW)
DEF_ICON_COLOR(SEQ_REMOVE_GAPS)
DEF_ICON_COLOR(SEQ_REMOVE_GAPS_ALL)
DEF_ICON_COLOR(SEQ_SEQUENCER)
DEF_ICON_COLOR(SEQ_SLIP_CONTENTS)
DEF_ICON_COLOR(SEQ_SNAP_STRIP)
DEF_ICON_COLOR(SEQ_SNAP_TO_FRAME)
DEF_ICON_COLOR(SEQ_SPLITVIEW)
DEF_ICON_COLOR(SEQ_STRIP_DUPLICATE)
DEF_ICON_COLOR(SEQ_STRIP_META)
DEF_ICON_COLOR(SEQ_SWAP_LEFT)
DEF_ICON_COLOR(SEQ_SWAP_RIGHT)
DEF_ICON_COLOR(SEQ_TEXT)
DEF_ICON_COLOR(SETTINGS)
DEF_ICON_COLOR(SETUP)
DEF_ICON_COLOR(SET_CURVE_HANDLE_POSITIONS)
DEF_ICON_COLOR(SET_CURVE_RADIUS)
DEF_ICON_COLOR(SET_CURVE_TILT)
DEF_ICON_COLOR(SET_FACE_SET)
DEF_ICON_COLOR(SET_FRAMES)
DEF_ICON_COLOR(SET_FROM_FACES)
DEF_ICON_COLOR(SET_ID)
DEF_ICON_COLOR(SET_LOWERCASE)
DEF_ICON_COLOR(SET_MATERIAL_INDEX)
DEF_ICON_COLOR(SET_POSITION)
DEF_ICON_COLOR(SET_ROLL)
DEF_ICON_COLOR(SET_SELECTION)
DEF_ICON_COLOR(SET_SMOOTH)
DEF_ICON_COLOR(SET_SHADE_SMOOTH)
DEF_ICON_COLOR(SET_SPLINE_RESOLUTION)
DEF_ICON_COLOR(SET_TIME)
DEF_ICON_COLOR(SET_UPPERCASE)
DEF_ICON_COLOR(SHADERFX)
DEF_ICON_COLOR(SHADER_ACTIVE)
DEF_ICON_COLOR(SHADING_BBOX)
DEF_ICON_COLOR(SHADING_EDGE_SHARP)
DEF_ICON_COLOR(SHADING_EDGE_SMOOTH)
DEF_ICON_COLOR(SHADING_FLAT)
DEF_ICON_COLOR(SHADING_RENDERED)
DEF_ICON_COLOR(SHADING_SMOOTH)
DEF_ICON_COLOR(SHADING_SOLID)
DEF_ICON_COLOR(SHADING_TEXTURE)
DEF_ICON_COLOR(SHADING_VERT_SHARP)
DEF_ICON_COLOR(SHADING_VERT_SMOOTH)
DEF_ICON_COLOR(SHADING_WIRE)
DEF_ICON_COLOR(SHAPE)
DEF_ICON_COLOR(SHAPEKEY_DATA)
DEF_ICON_COLOR(SHAPEPROPAGATE)
DEF_ICON_COLOR(SHARPCURVE)
DEF_ICON_COLOR(SHARPEN)
DEF_ICON_COLOR(SHEAR)
DEF_ICON_COLOR(SHORTDISPLAY)
DEF_ICON_COLOR(SHOW_UNSELECTED)
DEF_ICON_COLOR(SHRINK_FATTEN)
DEF_ICON_COLOR(SIBLINGS)
DEF_ICON_COLOR(SIMILAR)
DEF_ICON_COLOR(SIMPLIFY_ADAPTIVE)
DEF_ICON_COLOR(SIMPLIFY_SAMPLE)
DEF_ICON_COLOR(SLIDE_EDGE)
DEF_ICON_COLOR(SLIDE_VERTEX)
DEF_ICON_COLOR(SMALL_CAPS)
DEF_ICON_COLOR(SMOOTHCURVE)
DEF_ICON_COLOR(SMOOTH_KEYFRAMES)
DEF_ICON_COLOR(SMOOTH_LAPLACIAN)
DEF_ICON_COLOR(SMOOTH_RADIUS)
DEF_ICON_COLOR(SMOOTH_TILT)
DEF_ICON_COLOR(SMOOTH_VERTEX)
DEF_ICON_COLOR(SMOOTH_WEIGHT)
DEF_ICON_COLOR(SNAPTOPIXEL_CENTER)
DEF_ICON_COLOR(SNAPTOPIXEL_CORNER)
DEF_ICON_COLOR(SNAPTOPIXEL_OFF)
DEF_ICON_COLOR(SNAP_CURRENTFRAME)
DEF_ICON_COLOR(SNAP_CURSORVALUE)
DEF_ICON_COLOR(SNAP_EDGE)
DEF_ICON_COLOR(SNAP_FACE)
DEF_ICON_COLOR(SNAP_FACE_CENTER)
DEF_ICON_COLOR(SNAP_FACE_NEAREST)
DEF_ICON_COLOR(SNAP_GRID)
DEF_ICON_COLOR(SNAP_INCREMENT)
DEF_ICON_COLOR(SNAP_MIDPOINT)
DEF_ICON_COLOR(SNAP_NEARESTFRAME)
DEF_ICON_COLOR(SNAP_NEARESTMARKER)
DEF_ICON_COLOR(SNAP_NEARESTSECOND)
DEF_ICON_COLOR(SNAP_NORMAL)
DEF_ICON_COLOR(SNAP_OFF)
DEF_ICON_COLOR(SNAP_ON)
DEF_ICON_COLOR(SNAP_PEEL_OBJECT)
DEF_ICON_COLOR(SNAP_PERPENDICULAR)
DEF_ICON_COLOR(SNAP_STEP)
DEF_ICON_COLOR(SNAP_STEP_SECOND)
DEF_ICON_COLOR(SNAP_SURFACE)
DEF_ICON_COLOR(SNAP_SYMMETRY)
DEF_ICON_COLOR(SNAP_TO_ADJACENT)
DEF_ICON_COLOR(SNAP_TO_PIXELS)
DEF_ICON_COLOR(SNAP_VERTEX)
DEF_ICON_COLOR(SNAP_VOLUME)
DEF_ICON_COLOR(SOLIDIFY)
DEF_ICON_COLOR(SOLO_OFF)
DEF_ICON_COLOR(SOLO_ON)
DEF_ICON_COLOR(SORTALPHA)
DEF_ICON_COLOR(SORTBYEXT)
DEF_ICON_COLOR(SORTSIZE)
DEF_ICON_COLOR(SORTTIME)
DEF_ICON_COLOR(SORT_ASC)
DEF_ICON_COLOR(SORT_DESC)
DEF_ICON_COLOR(SOUND) /* Sound? Deprecated? */
DEF_ICON_COLOR(SPACE2)
DEF_ICON_COLOR(SPACE3)
DEF_ICON_COLOR(SPANISH_EXCLAMATION)
DEF_ICON_COLOR(SPANISH_QUESTION)
DEF_ICON_COLOR(SPEAKER)
DEF_ICON_COLOR(SPECIAL)
DEF_ICON_COLOR(SPHERE)
DEF_ICON_COLOR(SPHERECURVE)
DEF_ICON_COLOR(SPHEREPROJECT)
DEF_ICON_COLOR(SPIN)
DEF_ICON_COLOR(SPLINE_LENGTH)
DEF_ICON_COLOR(SPLINE_RESOLUTION)
DEF_ICON_COLOR(SPLINE_TYPE)
DEF_ICON_COLOR(SPLIT)
DEF_ICON_COLOR(SPLITBYEDGES)
DEF_ICON_COLOR(SPLITEDGE)
DEF_ICON_COLOR(SPLITSCREEN)
DEF_ICON_COLOR(SPLIT_BYVERTICES)
DEF_ICON_COLOR(SPLIT_CONCAVE)
DEF_ICON_COLOR(SPLIT_HORIZONTAL)
DEF_ICON_COLOR(SPLIT_NONPLANAR)
DEF_ICON_COLOR(SPLIT_TO_INSTANCES)
DEF_ICON_COLOR(SPLIT_VERTICAL)
DEF_ICON_COLOR(SPOT_BLEND)
DEF_ICON_COLOR(SPREADSHEET)
DEF_ICON_COLOR(STARTPOINT)
DEF_ICON_COLOR(STATUSBAR)
DEF_ICON_COLOR(STEPPED_MODIFIER)
DEF_ICON_COLOR(STICKY_UVS_DISABLE)
DEF_ICON_COLOR(STICKY_UVS_LOC)
DEF_ICON_COLOR(STICKY_UVS_VERT)
DEF_ICON_COLOR(STITCH)
DEF_ICON_COLOR(STRAIGHTEN)
DEF_ICON_COLOR(STRAIGHTEN_X)
DEF_ICON_COLOR(STRAIGHTEN_Y)
DEF_ICON_COLOR(STRING)
DEF_ICON_COLOR(STRING_FIND)
DEF_ICON_COLOR(STRING_JOIN)
DEF_ICON_COLOR(STRING_LENGTH)
DEF_ICON_COLOR(STRING_SUBSTRING)
DEF_ICON_COLOR(STRING_TO_CURVE)
DEF_ICON_COLOR(STROKE)
DEF_ICON_COLOR(STUCCI_TEX)
DEF_ICON_COLOR(STYLUS_PRESSURE)
DEF_ICON_COLOR(SUBDIVEDGELOOP)
DEF_ICON_COLOR(SUBDIVIDE_EDGES)
DEF_ICON_COLOR(SUBDIVIDE_MESH)
DEF_ICON_COLOR(SUBDIV_EDGERING)
DEF_ICON_COLOR(SUFFIX)
DEF_ICON_COLOR(SUPER_ONE)
DEF_ICON_COLOR(SUPER_THREE)
DEF_ICON_COLOR(SUPER_TWO)
DEF_ICON_COLOR(SURFACE_DATA)
DEF_ICON_COLOR(SURFACE_NCIRCLE)
DEF_ICON_COLOR(SURFACE_NCURVE)
DEF_ICON_COLOR(SURFACE_NCYLINDER)
DEF_ICON_COLOR(SURFACE_NSPHERE)
DEF_ICON_COLOR(SURFACE_NSURFACE)
DEF_ICON_COLOR(SURFACE_NTORUS)
DEF_ICON_COLOR(SURFACE_SMOOTH)
DEF_ICON_COLOR(SWAP)
DEF_ICON_COLOR(SWIRL)
DEF_ICON_COLOR(SWITCH)
DEF_ICON_COLOR(SWITCH_DIRECTION)
DEF_ICON_COLOR(SYMMETRIZE)
DEF_ICON_COLOR(SYNC)
DEF_ICON_COLOR(SYNTAX_OFF)
DEF_ICON_COLOR(SYNTAX_ON)
DEF_ICON_COLOR(SYSTEM)

/* letter T */

DEF_ICON_COLOR(TAG)
DEF_ICON_COLOR(TEMP)
DEF_ICON_COLOR(TEMPLATE)
DEF_ICON_COLOR(TEXT) /* Text */
DEF_ICON_COLOR(TEXTURE)
DEF_ICON_COLOR(TEXTURE_ACTIVE)
DEF_ICON_COLOR(TEXTURE_SHADED)
DEF_ICON_COLOR(THREE_DOTS)
DEF_ICON_COLOR(TILT)
DEF_ICON_COLOR(TIME)
DEF_ICON_COLOR(TOGGLECAPS_BOTH)
DEF_ICON_COLOR(TOGGLECAPS_DEFAULT)
DEF_ICON_COLOR(TOGGLECAPS_END)
DEF_ICON_COLOR(TOGGLECAPS_START)
DEF_ICON_COLOR(TOGGLE_CLOSE)
DEF_ICON_COLOR(TOGGLE_CYCLIC)
DEF_ICON_COLOR(TOGGLE_META)
DEF_ICON_COLOR(TOGGLE_NODE_MUTE)
DEF_ICON_COLOR(TOGGLE_NODE_OPTIONS)
DEF_ICON_COLOR(TOGGLE_NODE_PREVIEW)
DEF_ICON_COLOR(TOOLBAR) /* The toolbar editor */
DEF_ICON_COLOR(TOOL_SETTINGS)
DEF_ICON_COLOR(TOPBAR)
DEF_ICON_COLOR(TOSPHERE)
DEF_ICON_COLOR(TPAINT_HLT)
DEF_ICON_COLOR(TRACKER)
DEF_ICON_COLOR(TRACKER_DATA)
DEF_ICON_COLOR(TRACKING)
DEF_ICON_COLOR(TRACKING_BACKWARDS)
DEF_ICON_COLOR(TRACKING_BACKWARDS_SINGLE)
DEF_ICON_COLOR(TRACKING_CLEAR_BACKWARDS)
DEF_ICON_COLOR(TRACKING_CLEAR_FORWARDS)
DEF_ICON_COLOR(TRACKING_FORWARDS)
DEF_ICON_COLOR(TRACKING_FORWARDS_SINGLE)
DEF_ICON_COLOR(TRACKING_REFINE_BACKWARDS)
DEF_ICON_COLOR(TRACKING_REFINE_FORWARDS)
DEF_ICON_COLOR(TRADEMARK)
DEF_ICON_COLOR(TRANSFER_DATA)
DEF_ICON_COLOR(TRANSFER_DATA_LAYOUT)
DEF_ICON_COLOR(TRANSFER_SCULPT)
DEF_ICON_COLOR(TRANSFER_UV)
DEF_ICON_COLOR(TRANSFORM_DIRECTION)
DEF_ICON_COLOR(TRANSFORM_GIZMO)
DEF_ICON_COLOR(TRANSFORM_MIRROR)
DEF_ICON_COLOR(TRANSFORM_MOVE)
DEF_ICON_COLOR(TRANSFORM_ORIGINS)
DEF_ICON_COLOR(TRANSFORM_POINT)
DEF_ICON_COLOR(TRANSFORM_ROTATE)
DEF_ICON_COLOR(TRANSFORM_SCALE)
DEF_ICON_COLOR(TRANSITION)
DEF_ICON_COLOR(TRANSLATE_INSTANCE)
DEF_ICON_COLOR(TRANSPOSE_MATRIX)
DEF_ICON_COLOR(TRIANGULATE)
DEF_ICON_COLOR(TRIA_DOWN)
DEF_ICON_COLOR(TRIA_DOWN_BAR)
DEF_ICON_COLOR(TRIA_LEFT)
DEF_ICON_COLOR(TRIA_LEFT_BAR)
DEF_ICON_COLOR(TRIA_RIGHT)
DEF_ICON_COLOR(TRIA_RIGHT_BAR)
DEF_ICON_COLOR(TRIA_UP)
DEF_ICON_COLOR(TRIA_UP_BAR)
DEF_ICON_COLOR(TRISTOQUADS)
DEF_ICON_COLOR(TYPE)

/* letter U */

DEF_ICON_COLOR(UGLYPACKAGE)
DEF_ICON_COLOR(UI)
DEF_ICON_COLOR(UNCOMMENT)
DEF_ICON_COLOR(UNDERLINE)
DEF_ICON_COLOR(UNDERLINED)
DEF_ICON_COLOR(UNDO)
DEF_ICON_COLOR(UNDO_HISTORY)
DEF_ICON_COLOR(UNICODE)
DEF_ICON_COLOR(UNINDENT)


DEF_ICON_COLOR(UNLINKED)/*bfa - don't change the order of the icons*/
DEF_ICON_COLOR(LINKED)/*bfa - don't change the order of the icons*/
DEF_ICON_COLOR(UNLOCKED) /*bfa - don't change the order of the icons*/
DEF_ICON_COLOR(LOCKED)   /*bfa - don't change the order of the icons*/
DEF_ICON_COLOR(UNPINNED) /*bfa - don't change the order of the icons*/
DEF_ICON_COLOR(PINNED)   /*bfa - don't change the order of the icons*/

DEF_ICON_COLOR(UNSUBDIVIDE)
DEF_ICON_COLOR(UNWRAP_ABF)
DEF_ICON_COLOR(UNWRAP_LSCM)
DEF_ICON_COLOR(UNWRAP_MINSTRETCH)
DEF_ICON_COLOR(URL)
DEF_ICON_COLOR(USER)
DEF_ICON_COLOR(UV)
DEF_ICON_COLOR(UV_DATA)
DEF_ICON_COLOR(UV_EDGESEL)
DEF_ICON_COLOR(UV_FACESEL)
DEF_ICON_COLOR(UV_ISLANDSEL)
DEF_ICON_COLOR(UV_SYNC_SELECT)
DEF_ICON_COLOR(UV_VERTEXSEL)

/* letter V */

DEF_ICON_COLOR(VALUE_TO_SELECTION)
DEF_ICON_COLOR(VALUE_TO_STRING)
DEF_ICON_COLOR(VARIANCE)
DEF_ICON_COLOR(VECTOR_DISPLACE)
DEF_ICON_COLOR(VERTCOLFROMWEIGHT)
DEF_ICON_COLOR(VERTEXCONNECT)
DEF_ICON_COLOR(VERTEXCONNECTPATH)
DEF_ICON_COLOR(VERTEXSEL)
DEF_ICON_COLOR(VERTEX_CREASE)
DEF_ICON_COLOR(VERTEX_NEIGHBORS)
DEF_ICON_COLOR(VERTEX_OF_CORNER)
DEF_ICON_COLOR(VERTEX_PARENT)
DEF_ICON_COLOR(VIEW)
DEF_ICON_COLOR(VIEW3D)
DEF_ICON_COLOR(VIEWALL)
DEF_ICON_COLOR(VIEWALL_RESETCURSOR)
DEF_ICON_COLOR(VIEWCAMERACENTER)
DEF_ICON_COLOR(VIEWPORT_TRANSFORM)
DEF_ICON_COLOR(VIEWZOOM)
DEF_ICON_COLOR(VIEW_ACTIVE_BACK)
DEF_ICON_COLOR(VIEW_ACTIVE_BOTTOM)
DEF_ICON_COLOR(VIEW_ACTIVE_FRONT)
DEF_ICON_COLOR(VIEW_ACTIVE_LEFT)
DEF_ICON_COLOR(VIEW_ACTIVE_RIGHT)
DEF_ICON_COLOR(VIEW_ACTIVE_TOP)
DEF_ICON_COLOR(VIEW_BACK)
DEF_ICON_COLOR(VIEW_BOTTOM)
DEF_ICON_COLOR(VIEW_CAMERA)
DEF_ICON_COLOR(VIEW_CAMERA_UNSELECTED)
DEF_ICON_COLOR(VIEW_FILL)
DEF_ICON_COLOR(VIEW_FIT)
DEF_ICON_COLOR(VIEW_FRAME)
DEF_ICON_COLOR(VIEW_FRONT)
DEF_ICON_COLOR(VIEW_GLOBAL_LOCAL)
DEF_ICON_COLOR(VIEW_GRAPH)
DEF_ICON_COLOR(VIEW_GRAPH_ALL)
DEF_ICON_COLOR(VIEW_LEFT)
DEF_ICON_COLOR(VIEW_NAVIGATION)
DEF_ICON_COLOR(VIEW_ORTHO)
DEF_ICON_COLOR(VIEW_PAN)
DEF_ICON_COLOR(VIEW_PERSPECTIVE)
DEF_ICON_COLOR(VIEW_REMOVE_LOCAL)
DEF_ICON_COLOR(VIEW_RESET)
DEF_ICON_COLOR(VIEW_RIGHT)
DEF_ICON_COLOR(VIEW_SELECTED)
DEF_ICON_COLOR(VIEW_STRETCH)
DEF_ICON_COLOR(VIEW_SWITCHACTIVECAM)
DEF_ICON_COLOR(VIEW_SWITCHTOCAM)
DEF_ICON_COLOR(VIEW_TOP)
DEF_ICON_COLOR(VIEW_ZOOM)
DEF_ICON_COLOR(VISUALTRANSFORM)
DEF_ICON_COLOR(VISUAL_LOC_ROT)
DEF_ICON_COLOR(VISUAL_LOC_ROT_SCALE)
DEF_ICON_COLOR(VISUAL_LOC_SCALE)
DEF_ICON_COLOR(VISUAL_MOVE)
DEF_ICON_COLOR(VISUAL_ROTATE)
DEF_ICON_COLOR(VISUAL_ROT_SCALE)
DEF_ICON_COLOR(VISUAL_SCALE)

DEF_ICON_COLOR(VIS_SEL_11)/*bfa - don't change the order of these icons*/
DEF_ICON_COLOR(VIS_SEL_10)/*bfa - don't change the order of these icons*/
DEF_ICON_COLOR(VIS_SEL_01)/*bfa - don't change the order of these icons*/
DEF_ICON_COLOR(VIS_SEL_00)/*bfa - don't change the order of these icons*/

DEF_ICON_COLOR(VOLUME_CUBE)
DEF_ICON_COLOR(VOLUME_DATA)
DEF_ICON_COLOR(VOLUME_DISTRIBUTE)
DEF_ICON_COLOR(VOLUME_TO_MESH)
DEF_ICON_COLOR(VORONI_TEX)
DEF_ICON_COLOR(VPAINT_HLT)

/* letter W */

DEF_ICON_COLOR(WALK_NAVIGATION)
DEF_ICON_COLOR(WALL)
DEF_ICON_COLOR(WARNING)
DEF_ICON_COLOR(WEIGHT_CLEAN)
DEF_ICON_COLOR(WEIGHT_FIX_DEFORMS)
DEF_ICON_COLOR(WEIGHT_INVERT)
DEF_ICON_COLOR(WEIGHT_LEVELS)
DEF_ICON_COLOR(WEIGHT_LIMIT_TOTAL)
DEF_ICON_COLOR(WEIGHT_MIRROR)
DEF_ICON_COLOR(WEIGHT_NORMALIZE)
DEF_ICON_COLOR(WEIGHT_NORMALIZE_ALL)
DEF_ICON_COLOR(WEIGHT_QUANTIZE)
DEF_ICON_COLOR(WEIGHT_SMOOTH)
DEF_ICON_COLOR(WEIGHT_TRANSFER_WEIGHTS)
DEF_ICON_COLOR(WELD)
DEF_ICON_COLOR(WHITESPACE_SPACES)
DEF_ICON_COLOR(WHITESPACE_TABS)
DEF_ICON_COLOR(WHITE_BALANCE)
DEF_ICON_COLOR(WIDTH_SIZE)
DEF_ICON_COLOR(WINDING)
DEF_ICON_COLOR(WINDOW)
DEF_ICON_COLOR(WINDOW_CLOSE)
DEF_ICON_COLOR(WIREFRAME)
DEF_ICON_COLOR(WOOD_TEX)
DEF_ICON_COLOR(WORDWRAP_OFF)
DEF_ICON_COLOR(WORDWRAP_ON)
DEF_ICON_COLOR(WORKSPACE)
DEF_ICON_COLOR(WORLD)
DEF_ICON_COLOR(WPAINT_HLT)

/* letter X */

DEF_ICON_COLOR(X)
DEF_ICON_COLOR(XRAY)
DEF_ICON_COLOR(X_ICON)

/* letter Y */

DEF_ICON_COLOR(YEN)
DEF_ICON_COLOR(Y_ICON)

/* letter Z */

DEF_ICON_COLOR(ZOOMIN)
DEF_ICON_COLOR(ZOOMOUT)
DEF_ICON_COLOR(ZOOM_ALL)
DEF_ICON_COLOR(ZOOM_BORDER)
DEF_ICON_COLOR(ZOOM_CAMERA)
DEF_ICON_COLOR(ZOOM_IN)
DEF_ICON_COLOR(ZOOM_OUT)
DEF_ICON_COLOR(ZOOM_PREVIOUS)
DEF_ICON_COLOR(ZOOM_RESET)
DEF_ICON_COLOR(ZOOM_SELECTED)
DEF_ICON_COLOR(ZOOM_SET)
DEF_ICON_COLOR(Z_ICON)

/* letter unsorted */

DEF_ICON_MODIFIER(MOD_INSTANCE)
DEF_ICON_OBJECT_DATA(OUTLINER_DATA_HAIR)
DEF_ICON_OBJECT_DATA(OUTLINER_DATA_POINTCLOUD)
DEF_ICON_SHADING(TEXTURE_DATA) /*BFA - DEF_ICON_SHADING*/
DEF_ICON_SHADING(WORLD_DATA)   /*BFA - DEF_ICON_SHADING*/


/* ------------------------- large --------------------------*/

DEF_ICON_COLOR(CANCEL_LARGE)
DEF_ICON_COLOR(WARNING_LARGE)
DEF_ICON_COLOR(QUESTION_LARGE)
DEF_ICON_COLOR(INFO_LARGE)
DEF_ICON_COLOR(DISC_LARGE)
DEF_ICON_COLOR(DISK_DRIVE_LARGE)
DEF_ICON_COLOR(EXTERNAL_DRIVE_LARGE)
DEF_ICON_COLOR(FILE_FOLDER_LARGE)
DEF_ICON_COLOR(FILE_LARGE)
DEF_ICON_COLOR(FILE_PARENT_LARGE)
DEF_ICON_COLOR(NETWORK_DRIVE_LARGE)
DEF_ICON_COLOR(BLENDER_LARGE)
DEF_ICON_COLOR(BLENDER_LOGO_LARGE)


/* The following are used when creating the Event Icons. */

DEF_ICON(KEY_BACKSPACE)
DEF_ICON(KEY_BACKSPACE_FILLED)
DEF_ICON(KEY_COMMAND)
DEF_ICON(KEY_COMMAND_FILLED)
DEF_ICON(KEY_CONTROL)
DEF_ICON(KEY_CONTROL_FILLED)
DEF_ICON(KEY_EMPTY1)
DEF_ICON(KEY_EMPTY1_FILLED)
DEF_ICON(KEY_EMPTY2)
DEF_ICON(KEY_EMPTY2_FILLED)
DEF_ICON(KEY_EMPTY3)
DEF_ICON(KEY_EMPTY3_FILLED)
DEF_ICON(KEY_MENU)
DEF_ICON(KEY_MENU_FILLED)
DEF_ICON(KEY_OPTION)
DEF_ICON(KEY_OPTION_FILLED)
DEF_ICON(KEY_RETURN)
DEF_ICON(KEY_RETURN_FILLED)
DEF_ICON(KEY_RING)
DEF_ICON(KEY_RING_FILLED)
DEF_ICON(KEY_SHIFT)
DEF_ICON(KEY_SHIFT_FILLED)
DEF_ICON(KEY_TAB)
DEF_ICON(KEY_TAB_FILLED)
DEF_ICON(KEY_WINDOWS)
DEF_ICON(KEY_WINDOWS_FILLED)

/*----------------- newline END OF ICONSHEET -------------------------------------*/

/* The items above are initiated sequentially while the ones that
 * follow are initiated individually. Therefore this item marks
 * the boundary. Add regular SVG icons above this one. */

DEF_ICON_BLANK(LAST_SVG_ITEM)

/* Vector icons. */
DEF_ICON_VECTOR(RGB_RED)
DEF_ICON_VECTOR(RGB_GREEN)
DEF_ICON_VECTOR(RGB_BLUE)

DEF_ICON_VECTOR(KEYTYPE_KEYFRAME_VEC)
DEF_ICON_VECTOR(KEYTYPE_BREAKDOWN_VEC)
DEF_ICON_VECTOR(KEYTYPE_EXTREME_VEC)
DEF_ICON_VECTOR(KEYTYPE_JITTER_VEC)
DEF_ICON_VECTOR(KEYTYPE_MOVING_HOLD_VEC)
DEF_ICON_VECTOR(KEYTYPE_GENERATED_VEC)

DEF_ICON_VECTOR(HANDLETYPE_FREE_VEC)
DEF_ICON_VECTOR(HANDLETYPE_ALIGNED_VEC)
DEF_ICON_VECTOR(HANDLETYPE_VECTOR_VEC)
DEF_ICON_VECTOR(HANDLETYPE_AUTO_VEC)
DEF_ICON_VECTOR(HANDLETYPE_AUTO_CLAMP_VEC)

DEF_ICON_VECTOR(COLORSET_01_VEC)
DEF_ICON_VECTOR(COLORSET_02_VEC)
DEF_ICON_VECTOR(COLORSET_03_VEC)
DEF_ICON_VECTOR(COLORSET_04_VEC)
DEF_ICON_VECTOR(COLORSET_05_VEC)
DEF_ICON_VECTOR(COLORSET_06_VEC)
DEF_ICON_VECTOR(COLORSET_07_VEC)
DEF_ICON_VECTOR(COLORSET_08_VEC)
DEF_ICON_VECTOR(COLORSET_09_VEC)
DEF_ICON_VECTOR(COLORSET_10_VEC)
DEF_ICON_VECTOR(COLORSET_11_VEC)
DEF_ICON_VECTOR(COLORSET_12_VEC)
DEF_ICON_VECTOR(COLORSET_13_VEC)
DEF_ICON_VECTOR(COLORSET_14_VEC)
DEF_ICON_VECTOR(COLORSET_15_VEC)
DEF_ICON_VECTOR(COLORSET_16_VEC)
DEF_ICON_VECTOR(COLORSET_17_VEC)
DEF_ICON_VECTOR(COLORSET_18_VEC)
DEF_ICON_VECTOR(COLORSET_19_VEC)
DEF_ICON_VECTOR(COLORSET_20_VEC)

DEF_ICON_VECTOR(COLLECTION_COLOR_01)
DEF_ICON_VECTOR(COLLECTION_COLOR_02)
DEF_ICON_VECTOR(COLLECTION_COLOR_03)
DEF_ICON_VECTOR(COLLECTION_COLOR_04)
DEF_ICON_VECTOR(COLLECTION_COLOR_05)
DEF_ICON_VECTOR(COLLECTION_COLOR_06)
DEF_ICON_VECTOR(COLLECTION_COLOR_07)
DEF_ICON_VECTOR(COLLECTION_COLOR_08)

DEF_ICON_VECTOR(STRIP_COLOR_01)
DEF_ICON_VECTOR(STRIP_COLOR_02)
DEF_ICON_VECTOR(STRIP_COLOR_03)
DEF_ICON_VECTOR(STRIP_COLOR_04)
DEF_ICON_VECTOR(STRIP_COLOR_05)
DEF_ICON_VECTOR(STRIP_COLOR_06)
DEF_ICON_VECTOR(STRIP_COLOR_07)
DEF_ICON_VECTOR(STRIP_COLOR_08)
DEF_ICON_VECTOR(STRIP_COLOR_09)

// DEF_ICON_VECTOR(LIBRARY_DATA_INDIRECT) /*bfa - not in Bforartists!*/
DEF_ICON_VECTOR(LIBRARY_DATA_OVERRIDE_NONEDITABLE)

DEF_ICON_VECTOR(LAYERGROUP_COLOR_01)
DEF_ICON_VECTOR(LAYERGROUP_COLOR_02)
DEF_ICON_VECTOR(LAYERGROUP_COLOR_03)
DEF_ICON_VECTOR(LAYERGROUP_COLOR_04)
DEF_ICON_VECTOR(LAYERGROUP_COLOR_05)
DEF_ICON_VECTOR(LAYERGROUP_COLOR_06)
DEF_ICON_VECTOR(LAYERGROUP_COLOR_07)
DEF_ICON_VECTOR(LAYERGROUP_COLOR_08)

/* Events. */
DEF_ICON_COLOR(EVENT_A)
DEF_ICON_COLOR(EVENT_B)
DEF_ICON_COLOR(EVENT_C)
DEF_ICON_COLOR(EVENT_D)
DEF_ICON_COLOR(EVENT_E)
DEF_ICON_COLOR(EVENT_F)
DEF_ICON_COLOR(EVENT_G)
DEF_ICON_COLOR(EVENT_H)
DEF_ICON_COLOR(EVENT_I)
DEF_ICON_COLOR(EVENT_J)
DEF_ICON_COLOR(EVENT_K)
DEF_ICON_COLOR(EVENT_L)
DEF_ICON_COLOR(EVENT_M)
DEF_ICON_COLOR(EVENT_N)
DEF_ICON_COLOR(EVENT_O)
DEF_ICON_COLOR(EVENT_P)
DEF_ICON_COLOR(EVENT_Q)
DEF_ICON_COLOR(EVENT_R)
DEF_ICON_COLOR(EVENT_S)
DEF_ICON_COLOR(EVENT_T)
DEF_ICON_COLOR(EVENT_U)
DEF_ICON_COLOR(EVENT_V)
DEF_ICON_COLOR(EVENT_W)
DEF_ICON_COLOR(EVENT_X)
DEF_ICON_COLOR(EVENT_Y)
DEF_ICON_COLOR(EVENT_Z)
DEF_ICON_COLOR(EVENT_SHIFT)
DEF_ICON_COLOR(EVENT_CTRL)
DEF_ICON_COLOR(EVENT_ALT)
DEF_ICON_COLOR(EVENT_OS)
DEF_ICON_COLOR(EVENT_HYPER)

DEF_ICON_COLOR(EVENT_F1)
DEF_ICON_COLOR(EVENT_F2)
DEF_ICON_COLOR(EVENT_F3)
DEF_ICON_COLOR(EVENT_F4)
DEF_ICON_COLOR(EVENT_F5)
DEF_ICON_COLOR(EVENT_F6)
DEF_ICON_COLOR(EVENT_F7)
DEF_ICON_COLOR(EVENT_F8)
DEF_ICON_COLOR(EVENT_F9)
DEF_ICON_COLOR(EVENT_F10)
DEF_ICON_COLOR(EVENT_F11)
DEF_ICON_COLOR(EVENT_F12)
DEF_ICON_COLOR(EVENT_F13)
DEF_ICON_COLOR(EVENT_F14)
DEF_ICON_COLOR(EVENT_F15)
DEF_ICON_COLOR(EVENT_F16)
DEF_ICON_COLOR(EVENT_F17)
DEF_ICON_COLOR(EVENT_F18)
DEF_ICON_COLOR(EVENT_F19)
DEF_ICON_COLOR(EVENT_F20)
DEF_ICON_COLOR(EVENT_F21)
DEF_ICON_COLOR(EVENT_F22)
DEF_ICON_COLOR(EVENT_F23)
DEF_ICON_COLOR(EVENT_F24)

DEF_ICON_COLOR(EVENT_ESC)
DEF_ICON_COLOR(EVENT_TAB)
DEF_ICON_COLOR(EVENT_PAGEUP)
DEF_ICON_COLOR(EVENT_PAGEDOWN)
DEF_ICON_COLOR(EVENT_RETURN)
DEF_ICON_COLOR(EVENT_SPACEKEY)

DEF_ICON_COLOR(EVENT_ZEROKEY)
DEF_ICON_COLOR(EVENT_ONEKEY)
DEF_ICON_COLOR(EVENT_TWOKEY)
DEF_ICON_COLOR(EVENT_THREEKEY)
DEF_ICON_COLOR(EVENT_FOURKEY)
DEF_ICON_COLOR(EVENT_FIVEKEY)
DEF_ICON_COLOR(EVENT_SIXKEY)
DEF_ICON_COLOR(EVENT_SEVENKEY)
DEF_ICON_COLOR(EVENT_EIGHTKEY)
DEF_ICON_COLOR(EVENT_NINEKEY)

DEF_ICON_COLOR(EVENT_PAD0)
DEF_ICON_COLOR(EVENT_PAD1)
DEF_ICON_COLOR(EVENT_PAD2)
DEF_ICON_COLOR(EVENT_PAD3)
DEF_ICON_COLOR(EVENT_PAD4)
DEF_ICON_COLOR(EVENT_PAD5)
DEF_ICON_COLOR(EVENT_PAD6)
DEF_ICON_COLOR(EVENT_PAD7)
DEF_ICON_COLOR(EVENT_PAD8)
DEF_ICON_COLOR(EVENT_PAD9)
DEF_ICON_COLOR(EVENT_PADASTER)
DEF_ICON_COLOR(EVENT_PADSLASH)
DEF_ICON_COLOR(EVENT_PADMINUS)
DEF_ICON_COLOR(EVENT_PADENTER)
DEF_ICON_COLOR(EVENT_PADPLUS)
DEF_ICON_COLOR(EVENT_PADPERIOD)

DEF_ICON_COLOR(EVENT_MOUSE_4)
DEF_ICON_COLOR(EVENT_MOUSE_5)
DEF_ICON_COLOR(EVENT_MOUSE_6)
DEF_ICON_COLOR(EVENT_MOUSE_7)
DEF_ICON_COLOR(EVENT_TABLET_STYLUS)
DEF_ICON_COLOR(EVENT_TABLET_ERASER)
DEF_ICON_COLOR(EVENT_LEFT_ARROW)
DEF_ICON_COLOR(EVENT_DOWN_ARROW)
DEF_ICON_COLOR(EVENT_RIGHT_ARROW)
DEF_ICON_COLOR(EVENT_UP_ARROW)
DEF_ICON_COLOR(EVENT_PAUSE)
DEF_ICON_COLOR(EVENT_INSERT)
DEF_ICON_COLOR(EVENT_HOME)
DEF_ICON_COLOR(EVENT_END)
DEF_ICON_COLOR(EVENT_UNKNOWN)
DEF_ICON_COLOR(EVENT_GRLESS)
DEF_ICON_COLOR(EVENT_MEDIAPLAY)
DEF_ICON_COLOR(EVENT_MEDIASTOP)
DEF_ICON_COLOR(EVENT_MEDIAFIRST)
DEF_ICON_COLOR(EVENT_MEDIALAST)
DEF_ICON_COLOR(EVENT_APP)
DEF_ICON_COLOR(EVENT_CAPSLOCK)
DEF_ICON_COLOR(EVENT_BACKSPACE)
DEF_ICON_COLOR(EVENT_DEL)
DEF_ICON_COLOR(EVENT_SEMICOLON)
DEF_ICON_COLOR(EVENT_PERIOD)
DEF_ICON_COLOR(EVENT_COMMA)
DEF_ICON_COLOR(EVENT_QUOTE)
DEF_ICON_COLOR(EVENT_ACCENTGRAVE)
DEF_ICON_COLOR(EVENT_MINUS)
DEF_ICON_COLOR(EVENT_PLUS)
DEF_ICON_COLOR(EVENT_SLASH)
DEF_ICON_COLOR(EVENT_BACKSLASH)
DEF_ICON_COLOR(EVENT_EQUAL)
DEF_ICON_COLOR(EVENT_LEFTBRACKET)
DEF_ICON_COLOR(EVENT_RIGHTBRACKET)

DEF_ICON_COLOR(EVENT_PAD_PAN)
DEF_ICON_COLOR(EVENT_PAD_ROTATE)
DEF_ICON_COLOR(EVENT_PAD_ZOOM)

DEF_ICON_COLOR(EVENT_NDOF_BUTTON_V1)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_V2)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_V3)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_SAVE_V1)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_SAVE_V2)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_SAVE_V3)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_1)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_2)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_3)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_4)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_5)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_6)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_7)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_8)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_9)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_10)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_11)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_12)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_MENU)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_FIT)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_TOP)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_BOTTOM)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_LEFT)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_RIGHT)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_FRONT)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_BACK)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_ISO1)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_ISO2)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_ROLL_CW)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_ROLL_CCW)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_SPIN_CW)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_SPIN_CCW)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_TILT_CW)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_TILT_CCW)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_ROTATE)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_PANZOOM)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_DOMINANT)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_PLUS)
DEF_ICON_COLOR(EVENT_NDOF_BUTTON_MINUS)

/* Node Sockets */
DEF_ICON_VECTOR(NODE_SOCKET_FLOAT)
DEF_ICON_VECTOR(NODE_SOCKET_VECTOR)
DEF_ICON_VECTOR(NODE_SOCKET_RGBA)
DEF_ICON_VECTOR(NODE_SOCKET_SHADER)
DEF_ICON_VECTOR(NODE_SOCKET_BOOLEAN)
DEF_ICON_VECTOR(NODE_SOCKET_INT)
DEF_ICON_VECTOR(NODE_SOCKET_STRING)
DEF_ICON_VECTOR(NODE_SOCKET_OBJECT)
DEF_ICON_VECTOR(NODE_SOCKET_IMAGE)
DEF_ICON_VECTOR(NODE_SOCKET_GEOMETRY)
DEF_ICON_VECTOR(NODE_SOCKET_COLLECTION)
DEF_ICON_VECTOR(NODE_SOCKET_TEXTURE)
DEF_ICON_VECTOR(NODE_SOCKET_MATERIAL)
DEF_ICON_VECTOR(NODE_SOCKET_ROTATION)
DEF_ICON_VECTOR(NODE_SOCKET_MENU)
DEF_ICON_VECTOR(NODE_SOCKET_MATRIX)
DEF_ICON_VECTOR(NODE_SOCKET_BUNDLE)
DEF_ICON_VECTOR(NODE_SOCKET_CLOSURE)

/* add as needed. */

/* Undefine all types. */

#undef DEF_ICON
#undef DEF_ICON_ERROR
#undef DEF_ICON_SCENE
#undef DEF_ICON_COLLECTION
#undef DEF_ICON_OBJECT
#undef DEF_ICON_OBJECT_DATA
#undef DEF_ICON_MODIFIER
#undef DEF_ICON_SHADING
#undef DEF_ICON_FOLDER
#undef DEF_ICON_VECTOR
#undef DEF_ICON_COLOR
#undef DEF_ICON_FUND
#undef DEF_ICON_BLANK
