/* SPDX-FileCopyrightText: 2025 Blender Authors
 *
 * SPDX-License-Identifier: GPL-2.0-or-later */

#pragma once

#ifdef __cplusplus
extern "C" {
#endif

struct ARegion;

/* Initialize the toolshelf runtime data system */
void BKE_toolshelf_runtime_init(void);

/* Free all toolshelf runtime data */
void BKE_toolshelf_runtime_exit(void);

/* Get the category tabs offset for a region */
float BKE_toolshelf_category_tabs_offset_get(const struct ARegion *region);

/* Set the category tabs offset for a region */
void BKE_toolshelf_category_tabs_offset_set(struct ARegion *region, float offset);

/* Free runtime data for a specific region */
void BKE_toolshelf_region_free(struct ARegion *region);

#ifdef __cplusplus
}
#endif